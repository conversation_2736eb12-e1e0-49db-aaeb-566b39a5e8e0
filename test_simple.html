<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار الدوال</title>
</head>
<body>
    <h1>اختبار الدوال JavaScript</h1>
    <button onclick="testFunctions()">اختبار الدوال</button>
    <div id="result"></div>

    <script>
        function populateYearFilter(years) {
            console.log('دالة populateYearFilter تعمل');
            return true;
        }
        
        function updateTable(data, filterType) {
            console.log('دالة updateTable تعمل');
            return true;
        }
        
        function deleteCertificates() {
            console.log('دالة deleteCertificates تعمل');
            return true;
        }
        
        function testFunctions() {
            const result = document.getElementById('result');
            let output = '<h2>نتائج الاختبار:</h2>';
            
            try {
                populateYearFilter(['2024/2025']);
                output += '<p>✅ populateYearFilter تعمل</p>';
            } catch (e) {
                output += '<p>❌ populateYearFilter خطأ: ' + e.message + '</p>';
            }
            
            try {
                updateTable([], 'test');
                output += '<p>✅ updateTable تعمل</p>';
            } catch (e) {
                output += '<p>❌ updateTable خطأ: ' + e.message + '</p>';
            }
            
            try {
                deleteCertificates();
                output += '<p>✅ deleteCertificates تعمل</p>';
            } catch (e) {
                output += '<p>❌ deleteCertificates خطأ: ' + e.message + '</p>';
            }
            
            result.innerHTML = output;
        }
    </script>
</body>
</html>
