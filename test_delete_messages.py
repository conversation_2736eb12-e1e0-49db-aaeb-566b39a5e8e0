#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار رسائل الحذف المحسنة في sub199_window_html.py
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QInputDialog, QLineEdit

def test_delete_messages():
    """اختبار رسائل الحذف بنفس الأسلوب المستخدم في التطبيق"""
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(2)  # RTL
    
    print("🧪 اختبار رسائل الحذف المحسنة...")
    
    # محاكاة البيانات
    records_count = 25
    notes_filter = "لائحة التحويلات (المغادرون)"  
    year_filter = "2024-2025"
    
    # المرحلة الأولى: رسالة التحذير
    print("📝 المرحلة 1: رسالة التحذير الأولى")
    confirm_dialog = QMessageBox()
    confirm_dialog.setWindowTitle("🗑️ تحذير: حذف جميع السجلات")
    confirm_dialog.setText("تحذير: حذف السجلات المحددة")
    confirm_dialog.setInformativeText(
        f"ستقوم هذه العملية بحذف {records_count} سجل نهائياً:\n\n"
        f"التصفية المحددة:\n"
        f"• النوع: {notes_filter}\n"
        f"• السنة الدراسية: {year_filter}\n\n"
        "⚠️ لا يمكن التراجع عن هذه العملية\n\n"
        "هل تريد المتابعة؟"
    )
    confirm_dialog.setIcon(QMessageBox.Warning)
    confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    confirm_dialog.setDefaultButton(QMessageBox.No)
    confirm_dialog.button(QMessageBox.Yes).setText("نعم، متابعة")
    confirm_dialog.button(QMessageBox.No).setText("إلغاء")
    
    confirm_dialog.setStyleSheet("""
        QMessageBox {
            background-color: white;
            font-family: 'Calibri';
            font-size: 13pt;
        }
        QMessageBox QLabel {
            font-family: 'Calibri';
            font-size: 13pt;
            font-weight: bold;
            color: #c0392b;
        }
        QPushButton {
            font-family: 'Calibri';
            font-size: 13pt;
            font-weight: bold;
            min-width: 80px;
            padding: 8px;
            margin: 4px;
        }
    """)
    
    if confirm_dialog.exec_() != QMessageBox.Yes:
        print("❌ المستخدم ألغى في المرحلة 1")
        return
    
    # المرحلة الثانية: طلب رمز التأكيد
    print("🔐 المرحلة 2: طلب رمز التأكيد")
    password_dialog = QInputDialog()
    password_dialog.setWindowTitle("🔐 رمز التأكيد")
    password_dialog.setLabelText("الرجاء إدخال رمز التأكيد (12345) للمتابعة:")
    password_dialog.setTextEchoMode(QLineEdit.Password)
    password_dialog.setStyleSheet("""
        QInputDialog {
            background-color: white;
            font-family: 'Calibri';
        }
        QLabel {
            font-family: 'Calibri';
            font-size: 15pt;
            font-weight: bold;
            color: #2c3e50;
        }
        QLineEdit {
            padding: 8px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            font-family: 'Calibri';
            font-size: 15pt;
        }
        QPushButton {
            font-family: 'Calibri';
            font-size: 15pt;
            font-weight: bold;
            min-width: 80px;
            padding: 5px;
        }
    """)
    
    ok = password_dialog.exec_()
    password = password_dialog.textValue()
    
    if not ok or password != "12345":
        print("❌ رمز خاطئ في المرحلة 2:", password)
        error_dialog = QMessageBox()
        error_dialog.setWindowTitle("❌ خطأ في التحقق")
        error_dialog.setText("رمز التأكيد غير صحيح")
        error_dialog.setInformativeText("لم يتم حذف السجلات - الرمز المُدخل غير صحيح")
        error_dialog.setIcon(QMessageBox.Critical)
        error_dialog.setStyleSheet("""
            QMessageBox { background-color: white; font-family: 'Calibri'; }
            QLabel { font-family: 'Calibri'; font-size: 13pt; font-weight: bold; color: #c0392b; }
        """)
        error_dialog.exec_()
        return
    
    # المرحلة الثالثة: تأكيد نهائي
    print("⚠️ المرحلة 3: التأكيد النهائي")
    final_dialog = QMessageBox()
    final_dialog.setWindowTitle("⚠️ تأكيد نهائي للحذف")
    final_dialog.setText("هذا تحذير أخير قبل حذف البيانات")
    final_dialog.setInformativeText(
        f"عدد السجلات: {records_count} سجل\n"
        f"النوع: {notes_filter}\n"
        f"السنة: {year_filter}\n\n"
        "هل تريد بالتأكيد المتابعة؟"
    )
    final_dialog.setIcon(QMessageBox.Warning)
    final_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    final_dialog.setDefaultButton(QMessageBox.No)
    final_dialog.button(QMessageBox.Yes).setText("نعم، تنفيذ الحذف")
    final_dialog.button(QMessageBox.No).setText("إلغاء")
    
    if final_dialog.exec_() != QMessageBox.Yes:
        print("❌ المستخدم ألغى في المرحلة 3")
        return
    
    # رسالة النجاح
    print("✅ جميع المراحل مكتملة - محاكاة النجاح")
    success_dialog = QMessageBox()
    success_dialog.setWindowTitle("✅ نجح الحذف")
    success_dialog.setText("تم حذف السجلات بنجاح!")
    
    details_text = f"📊 نتائج الحذف:\n\n"
    details_text += f"✅ تم حذف {records_count} سجل بنجاح\n"
    details_text += f"📋 النوع: {notes_filter}\n"
    details_text += f"📅 السنة: {year_filter}"
    
    success_dialog.setInformativeText(details_text)
    success_dialog.setIcon(QMessageBox.Information)
    success_dialog.setStandardButtons(QMessageBox.Ok)
    
    # إضافة أيقونة البرنامج إلى نافذة الرسالة
    icon_path = "01.ico"
    if os.path.exists(icon_path):
        from PyQt5.QtGui import QIcon
        success_dialog.setWindowIcon(QIcon(icon_path))
    
    success_dialog.setStyleSheet("""
        QMessageBox {
            background-color: white;
            font-family: 'Calibri';
        }
        QLabel {
            font-family: 'Calibri';
            font-size: 13pt;
            font-weight: bold;
            color: #27ae60;
        }
        QPushButton {
            font-family: 'Calibri';
            font-size: 13pt;
            font-weight: bold;
            color: white;
            background-color: #27ae60;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            min-width: 100px;
        }
        QPushButton:hover {
            background-color: #219653;
        }
        QPushButton:pressed {
            background-color: #1e7e34;
        }
    """)
    
    success_dialog.exec_()
    print("🎉 اكتمال الاختبار بنجاح!")

if __name__ == "__main__":
    test_delete_messages()