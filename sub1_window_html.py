"""
نظام استيراد البيانات الحديث - تحويل sub1_window.py إلى منهجية Python + HTML
يجمع بين قوة Python في معالجة البيانات مع واجهة HTML تفاعلية عبر QWebEngineView

تحويل من sub1_window.py إلى نظام بايثون + HTML بدون خادم
"""

import sys
import os
import json
import sqlite3
import re
from database_config import get_database_path, get_database_connection
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFileDialog, QMessageBox
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel
from PyQt5.QtCore import QObject, pyqtSlot, QUrl, QDateTime, pyqtSignal, Qt
from PyQt5.QtGui import QIcon

# محاولة استيراد pandas مع معالجة الخطأ
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

class ArrivalsImporter:
    def __init__(self, log_callback):
        """فئة مخصصة لاستيراد بيانات الوافدين والمغادرين من ملفات Excel إلى قاعدة البيانات"""
        self.log_callback = log_callback
        self.db_path = get_database_path()

        # التحقق من توفر pandas وإضافة تحذير إذا لم تكن متوفرة
        if not PANDAS_AVAILABLE:
            self.log("تنبيه: مكتبة pandas غير متوفرة. لن تعمل وظائف استيراد البيانات من Excel.", "warning")
            self.log("لتثبيت pandas، استخدم الأمر: pip install pandas --only-binary=:all:", "info")

    def log(self, message, status="info"):
        """إضافة رسالة إلى سجل العمليات مع لون مميز"""
        self.log_callback(message, status)

    def create_arrivals_departures_table(self, cursor):
        """إنشاء جدول سجلات الوافدين والمغادرين بالهيكل المطلوب إذا لم يكن موجوداً"""
        try:
            # التحقق من وجود الجدول أولاً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='سجلات_الوافدين_والمغادرين'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                self.log("✅ جدول سجلات_الوافدين_والمغادرين موجود مسبقاً، سيتم إضافة البيانات الجديدة", "info")
                
                # التحقق من وجود جميع الأعمدة المطلوبة وإضافة المفقود منها
                cursor.execute("PRAGMA table_info(سجلات_الوافدين_والمغادرين)")
                existing_columns = [col[1] for col in cursor.fetchall()]
                
                required_columns = [
                    'رقم_التلميذ', 'النسب', 'الإسم', 'تاريخ_التحويل', 'نوع_التحويل',
                    'مؤسسة_الإستقبال', 'المؤسسة_الأصلية', 'المديرية_الإقليمية_الأصلية',
                    'الأكاديمية_الأصلية', 'المستوى', 'السنة_الدراسية', 'ملاحظات',
                    'اسم_الملف', 'تاريخ_الاستيراد'
                ]
                
                # إضافة الأعمدة المفقودة
                columns_added = 0
                for column in required_columns:
                    if column not in existing_columns:
                        try:
                            cursor.execute(f"ALTER TABLE سجلات_الوافدين_والمغادرين ADD COLUMN {column} TEXT")
                            self.log(f"تم إضافة العمود المفقود: {column}", "success")
                            columns_added += 1
                        except Exception as e:
                            self.log(f"خطأ في إضافة العمود {column}: {str(e)}", "warning")
                
                if columns_added > 0:
                    self.log(f"تم إضافة {columns_added} عمود مفقود للجدول", "success")
                else:
                    self.log("جميع الأعمدة المطلوبة موجودة", "info")
                    
            else:
                # إنشاء الجدول الجديد
                self.log("إنشاء جدول سجلات_الوافدين_والمغادرين الجديد...", "progress")
                
                cursor.execute("""
                    CREATE TABLE سجلات_الوافدين_والمغادرين (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        رقم_التلميذ TEXT,
                        النسب TEXT,
                        الإسم TEXT,
                        تاريخ_التحويل TEXT,
                        نوع_التحويل TEXT,
                        مؤسسة_الإستقبال TEXT,
                        المؤسسة_الأصلية TEXT,
                        المديرية_الإقليمية_الأصلية TEXT,
                        الأكاديمية_الأصلية TEXT,
                        المستوى TEXT,
                        السنة_الدراسية TEXT,
                        ملاحظات TEXT,
                        اسم_الملف TEXT,
                        تاريخ_الاستيراد TEXT,
                        UNIQUE(رقم_التلميذ, الإسم, تاريخ_التحويل, نوع_التحويل, السنة_الدراسية)
                    )
                """)
                
                # إنشاء الفهارس
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_arrivals_type ON سجلات_الوافدين_والمغادرين(نوع_التحويل)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_arrivals_name ON سجلات_الوافدين_والمغادرين(الإسم)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_arrivals_year ON سجلات_الوافدين_والمغادرين(السنة_الدراسية)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_arrivals_level ON سجلات_الوافدين_والمغادرين(المستوى)")
                
                self.log("تم إنشاء جدول سجلات_الوافدين_والمغادرين الجديد بالقيد الفريد بنجاح", "success")
                
        except Exception as e:
            raise Exception(f"خطأ في إنشاء/تحديث جدول سجلات_الوافدين_والمغادرين: {str(e)}")

    def extract_special_fields(self, df):
        """استخراج السنة الدراسية والملاحظات والمستوى من مواقع محددة"""
        سنة_دراسية = ""
        ملاحظات = ""
        مستوى = ""
        
        try:
            # استخراج السنة الدراسية من العمود G الصف 7 (الفهرس 6)
            if len(df) > 6 and len(df.columns) > 6:
                سنة_دراسية_value = df.iloc[6, 6]  # الصف 7، العمود G
                if pd.notna(سنة_دراسية_value):
                    سنة_دراسية = str(سنة_دراسية_value).strip()
                    self.log(f"تم استخراج السنة الدراسية: {سنة_دراسية}", "success")
        except Exception as e:
            self.log(f"خطأ في استخراج السنة الدراسية: {str(e)}", "warning")
        
        try:
            # استخراج الملاحظات من العمود D الصف 2 (الفهرس 1)
            if len(df) > 1 and len(df.columns) > 3:
                ملاحظات_value = df.iloc[1, 3]  # الصف 2، العمود D
                if pd.notna(ملاحظات_value):
                    ملاحظات = str(ملاحظات_value).strip()
                    self.log(f"تم استخراج الملاحظات: {ملاحظات}", "success")
        except Exception as e:
            self.log(f"خطأ في استخراج الملاحظات: {str(e)}", "warning")
        
        try:
            # استخراج المستوى من العمود C الصف 7 (الفهرس 6، العمود 2)
            if len(df) > 6 and len(df.columns) > 2:
                مستوى_value = df.iloc[6, 2]  # الصف 7، العمود C
                if pd.notna(مستوى_value):
                    مستوى = str(مستوى_value).strip()
                    self.log(f"تم استخراج المستوى: {مستوى}", "success")
        except Exception as e:
            self.log(f"خطأ في استخراج المستوى: {str(e)}", "warning")
        
        return سنة_دراسية, ملاحظات, مستوى

    def identify_columns(self, columns, ملاحظات=""):
        """تحديد الأعمدة المطلوبة من أسماء الأعمدة الموجودة في ملف إكسل مع مراعاة نوع اللائحة"""
        self.log(f"الأعمدة الموجودة في الملف: {list(columns)}", "info")
        self.log(f"نوع اللائحة: {ملاحظات}", "info")
        
        available_columns = list(columns)
        if len(available_columns) == 0:
            self.log("لا توجد أعمدة في الملف", "error")
            return None
        
        # استخدام الأعمدة من A إلى I مباشرة (الفهارس 0-8)
        column_mapping = {}
        
        if len(available_columns) >= 9:
            self.log("استخدام تخطيط الأعمدة من A إلى I", "info")
            
            # تحديد ترتيب الأعمدة حسب نوع اللائحة
            if "المغادرون" in ملاحظات:
                # في حالة المغادرون: مؤسسة_الإستقبال = G، المؤسسة_الأصلية = F
                self.log("🔄 تطبيق ترتيب أعمدة لائحة المغادرون", "info")
                column_mapping = {
                    'رقم_التلميذ': available_columns[0],               # العمود A
                    'النسب': available_columns[1],                    # العمود B
                    'الإسم': available_columns[2],                    # العمود C
                    'تاريخ_التحويل': available_columns[3],            # العمود D
                    'نوع_التحويل': available_columns[4],             # العمود E
                    'المؤسسة_الأصلية': available_columns[5],          # العمود F
                    'مؤسسة_الإستقبال': available_columns[6],         # العمود G
                    'المديرية_الإقليمية_الأصلية': available_columns[7], # العمود H
                    'الأكاديمية_الأصلية': available_columns[8],       # العمود I
                }
            else:
                # في حالة الوافدون أو أي حالة أخرى: مؤسسة_الإستقبال = F، المؤسسة_الأصلية = G
                self.log("🔄 تطبيق ترتيب أعمدة لائحة الوافدون", "info")
                column_mapping = {
                    'رقم_التلميذ': available_columns[0],               # العمود A
                    'النسب': available_columns[1],                    # العمود B
                    'الإسم': available_columns[2],                    # العمود C
                    'تاريخ_التحويل': available_columns[3],            # العمود D
                    'نوع_التحويل': available_columns[4],             # العمود E
                    'مؤسسة_الإستقبال': available_columns[5],         # العمود F
                    'المؤسسة_الأصلية': available_columns[6],          # العمود G
                    'المديرية_الإقليمية_الأصلية': available_columns[7], # العمود H
                    'الأكاديمية_الأصلية': available_columns[8],       # العمود I
                }
        else:
            self.log(f"عدد الأعمدة غير كافي: {len(available_columns)}, مطلوب 9 أعمدة على الأقل", "error")
            return None
        
        return column_mapping

    def update_academies_data(self, cursor):
        """تحديث بيانات الأكاديميات بناءً على المديريات الإقليمية"""
        try:
            self.log("🔄 بدء عملية تحديث بيانات الأكاديميات...", "progress")
            
            # التحقق من وجود جدول الأكاديميات
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الأكاديميات'")
            if not cursor.fetchone():
                self.log("⚠️ لم يتم العثور على جدول الأكاديميات. تم تخطي عملية التحديث.", "warning")
                return
            
            # التحقق من وجود الأعمدة المطلوبة في جدول الأكاديميات
            cursor.execute("PRAGMA table_info(الأكاديميات)")
            columns_info = cursor.fetchall()
            column_names = [col[1] for col in columns_info]
            
            if 'الأكاديمية' not in column_names or 'المديرية' not in column_names:
                self.log("❌ جدول الأكاديميات لا يحتوي على الأعمدة المطلوبة (الأكاديمية، المديرية)", "error")
                return
            
            # عرض إحصائيات قبل التحديث
            cursor.execute("""
                SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين 
                WHERE (الأكاديمية_الأصلية IS NULL OR الأكاديمية_الأصلية = '')
                AND (المديرية_الإقليمية_الأصلية IS NOT NULL AND المديرية_الإقليمية_الأصلية != '')
            """)
            records_to_update = cursor.fetchone()[0]
            
            if records_to_update == 0:
                self.log("ℹ️ لا توجد سجلات تحتاج لتحديث الأكاديمية", "info")
                return
            
            self.log(f"📊 عدد السجلات التي تحتاج لتحديث: {records_to_update}", "info")
            
            # تنفيذ عملية التحديث
            update_query = """
                UPDATE سجلات_الوافدين_والمغادرين 
                SET الأكاديمية_الأصلية = (
                    SELECT الأكاديمية 
                    FROM الأكاديميات 
                    WHERE الأكاديميات.المديرية = سجلات_الوافدين_والمغادرين.المديرية_الإقليمية_الأصلية
                    LIMIT 1
                )
                WHERE (الأكاديمية_الأصلية IS NULL OR الأكاديمية_الأصلية = '')
                AND (المديرية_الإقليمية_الأصلية IS NOT NULL AND المديرية_الإقليمية_الأصلية != '')
                AND EXISTS (
                    SELECT 1 FROM الأكاديميات 
                    WHERE الأكاديميات.المديرية = سجلات_الوافدين_والمغادرين.المديرية_الإقليمية_الأصلية
                )
            """
            
            cursor.execute(update_query)
            updated_rows = cursor.rowcount
            
            # عرض نتائج التحديث
            if updated_rows > 0:
                self.log(f"✅ تم تحديث {updated_rows} سجل بنجاح", "success")
                
                # عرض إحصائيات مفصلة
                cursor.execute("""
                    SELECT المديرية_الإقليمية_الأصلية, الأكاديمية_الأصلية, COUNT(*) as عدد_السجلات
                    FROM سجلات_الوافدين_والمغادرين 
                    WHERE الأكاديمية_الأصلية IS NOT NULL AND الأكاديمية_الأصلية != ''
                    GROUP BY المديرية_الإقليمية_الأصلية, الأكاديمية_الأصلية
                    ORDER BY عدد_السجلات DESC
                    LIMIT 10
                """)
                
                updated_stats = cursor.fetchall()
                if updated_stats:
                    self.log("📋 أهم التحديثات:", "info")
                    for مديرية, أكاديمية, عدد in updated_stats:
                        self.log(f"   • {مديرية} ← {أكاديمية}: {عدد} سجل", "info")
            else:
                self.log("⚠️ لم يتم تحديث أي سجل. تحقق من توافق أسماء المديريات", "warning")
                
                # عرض المديريات غير المطابقة
                cursor.execute("""
                    SELECT DISTINCT المديرية_الإقليمية_الأصلية
                    FROM سجلات_الوافدين_والمغادرين 
                    WHERE (الأكاديمية_الأصلية IS NULL OR الأكاديمية_الأصلية = '')
                    AND (المديرية_الإقليمية_الأصلية IS NOT NULL AND المديرية_الإقليمية_الأصلية != '')
                    AND المديرية_الإقليمية_الأصلية NOT IN (
                        SELECT المديرية FROM الأكاديميات WHERE المديرية IS NOT NULL
                    )
                    LIMIT 5
                """)
                
                unmatched_dirs = cursor.fetchall()
                if unmatched_dirs:
                    self.log("❌ مديريات غير موجودة في جدول الأكاديميات:", "warning")
                    for (مديرية,) in unmatched_dirs:
                        self.log(f"   • {مديرية}", "warning")
            
        except Exception as e:
            self.log(f"❌ خطأ في تحديث بيانات الأكاديميات: {str(e)}", "error")

class DataImportEngine(QObject):
    """محرك استيراد البيانات - مسؤول عن كل المعالجة والتفاعل مع قاعدة البيانات"""
    
    # إشارات للتواصل مع واجهة HTML
    logUpdated = pyqtSignal(str, str, str)  # message, status, timestamp
    importProgressUpdated = pyqtSignal(int, str)  # progress percentage, status text
    statisticsUpdated = pyqtSignal(str)  # statistics JSON
    
    def __init__(self):
        super().__init__()
        self.db_path = get_database_path()
        self.pandas_available = PANDAS_AVAILABLE
    
    def emit_log(self, message, status="info"):
        """إرسال رسالة إلى واجهة HTML"""
        timestamp = QDateTime.currentDateTime().toString("hh:mm:ss")
        self.logUpdated.emit(message, status, timestamp)
    
    @pyqtSlot(result=bool)
    def checkPandasAvailability(self):
        """التحقق من توفر pandas"""
        return self.pandas_available
    
    @pyqtSlot(result=str)
    def getDatabasePath(self):
        """الحصول على مسار قاعدة البيانات"""
        return self.db_path
    
    @pyqtSlot(result=str)
    def getDatabaseStatistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        try:
            if not os.path.exists(self.db_path):
                return json.dumps({
                    "current_year": "غير محدد",
                    "sections_count": 0,
                    "students_count": 0,
                    "levels_count": 0,
                    "teachers_count": 0,
                    "secret_codes_count": 0,
                    "sections": []
                }, ensure_ascii=False)
            
            conn = get_database_connection()
            cursor = conn.cursor()
            
            # جمع الإحصائيات
            stats = {}
            
            # السنة الدراسية الحالية
            try:
                cursor.execute("SELECT DISTINCT السنة_الدراسية FROM اللوائح ORDER BY السنة_الدراسية DESC LIMIT 1")
                result = cursor.fetchone()
                stats['current_year'] = result[0] if result and result[0] else "غير محدد"
            except:
                stats['current_year'] = "غير محدد"
            
            # إحصائيات الأقسام والتلاميذ
            try:
                if stats['current_year'] != "غير محدد":
                    cursor.execute("""
                        SELECT 
                            COUNT(DISTINCT القسم) as sections_count,
                            COUNT(*) as students_count,
                            COUNT(DISTINCT المستوى) as levels_count
                        FROM اللوائح 
                        WHERE السنة_الدراسية = ?
                    """, (stats['current_year'],))
                    
                    db_stats = cursor.fetchone()
                    if db_stats:
                        stats['sections_count'] = db_stats[0]
                        stats['students_count'] = db_stats[1] 
                        stats['levels_count'] = db_stats[2]
                    else:
                        stats['sections_count'] = 0
                        stats['students_count'] = 0
                        stats['levels_count'] = 0
                else:
                    # إذا لم تكن هناك سنة دراسية، احصل على إجمالي البيانات
                    cursor.execute("""
                        SELECT 
                            COUNT(DISTINCT القسم) as sections_count,
                            COUNT(*) as students_count,
                            COUNT(DISTINCT المستوى) as levels_count
                        FROM اللوائح
                    """)
                    
                    db_stats = cursor.fetchone()
                    if db_stats:
                        stats['sections_count'] = db_stats[0]
                        stats['students_count'] = db_stats[1] 
                        stats['levels_count'] = db_stats[2]
                    else:
                        stats['sections_count'] = 0
                        stats['students_count'] = 0
                        stats['levels_count'] = 0
            except:
                stats['sections_count'] = 0
                stats['students_count'] = 0
                stats['levels_count'] = 0
            
            # قائمة الأقسام
            try:
                if stats['current_year'] != "غير محدد":
                    cursor.execute("SELECT DISTINCT القسم FROM اللوائح WHERE السنة_الدراسية = ? ORDER BY القسم", (stats['current_year'],))
                else:
                    cursor.execute("SELECT DISTINCT القسم FROM اللوائح ORDER BY القسم")
                sections = [row[0] for row in cursor.fetchall()]
                stats['sections'] = sections
            except:
                stats['sections'] = []
            
            # إحصائيات الأساتذة
            try:
                cursor.execute("SELECT COUNT(*) FROM الأساتذة")
                result = cursor.fetchone()
                stats['teachers_count'] = result[0] if result else 0
            except:
                stats['teachers_count'] = 0
            
            # إحصائيات الرموز السرية
            try:
                cursor.execute("SELECT COUNT(*) FROM الرمز_السري")
                result = cursor.fetchone()
                stats['secret_codes_count'] = result[0] if result else 0
            except:
                stats['secret_codes_count'] = 0
            
            conn.close()
            return json.dumps(stats, ensure_ascii=False)
            
        except Exception as e:
            self.emit_log(f"خطأ في جمع الإحصائيات: {str(e)}", "error")
            return json.dumps({
                "error": str(e),
                "current_year": "غير محدد",
                "sections_count": 0,
                "students_count": 0,
                "levels_count": 0,
                "teachers_count": 0,
                "secret_codes_count": 0,
                "sections": []
            }, ensure_ascii=False)
    
    @pyqtSlot()
    def selectMasarFile(self):
        """اختيار ملف لوائح منظومة مسار"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            self.emit_log("لتثبيت pandas، استخدم الأمر: pip install pandas", "info")
            return
        
        # استخدام QFileDialog لاختيار الملف
        file_path, _ = QFileDialog.getOpenFileName(
            None, "اختر ملف الإكسل", "", "Excel Files (*.xlsx *.xls)"
        )
        
        if file_path:
            self.importMasarData(file_path)
    
    def importMasarData(self, file_path):
        """استيراد بيانات منظومة مسار"""
        self.emit_log("بدء استيراد بيانات منظومة مسار...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير الملف...")
        
        conn = None
        try:
            # التحقق من اسم الملف
            file_name = os.path.basename(file_path)
            if "ListEleve" not in file_name:
                self.emit_log(f"تحذير: الملف {file_name} قد لا يكون ملف لوائح منظومة مسار", "warning")
            
            # إنشاء اتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()
            
            # حذف السجلات الافتراضية
            self.importProgressUpdated.emit(10, "جاري حذف السجلات الافتراضية...")
            cursor.execute("DELETE FROM السجل_العام WHERE الرمز = 'A12345678'")
            cursor.execute("DELETE FROM اللوائح WHERE الرمز = 'A12345678'")
            cursor.execute("DROP TABLE IF EXISTS 'السجل_الاولي'")
            
            # قراءة ملف Excel
            self.importProgressUpdated.emit(20, "جاري قراءة ملف Excel...")
            sheets_dict = pd.read_excel(file_path, sheet_name=None)
            
            # استخراج السنة الدراسية
            current_academic_year = None
            for sheet_name, df in sheets_dict.items():
                if "Unnamed: 6" in df.columns and len(df) > 5:
                    current_academic_year = df.iloc[5]["Unnamed: 6"]
                    break
            
            # تحضير البيانات
            self.importProgressUpdated.emit(30, "جاري تحضير البيانات...")
            total_sheets = len(sheets_dict)
            
            for i, (sheet_name, df) in enumerate(sheets_dict.items()):
                progress = 30 + int((i / total_sheets) * 20)
                self.importProgressUpdated.emit(progress, f"جاري معالجة القسم {sheet_name}...")
                
                level_rows = df[df["Unnamed: 0"].astype(str).str.strip() == ": المستوى"]
                level_value = level_rows.iloc[0]["Unnamed: 2"] if not level_rows.empty else None
                year_value = df.iloc[5]["Unnamed: 6"] if "Unnamed: 6" in df.columns and len(df) > 5 else None
                
                df["القسم"] = sheet_name
                df["المستوى"] = level_value
                df["السنة الدراسية"] = year_value
            
            # دمج وحفظ البيانات
            self.importProgressUpdated.emit(50, "جاري دمج وحفظ البيانات...")
            combined_df = pd.concat(sheets_dict.values(), ignore_index=True)
            combined_df.to_sql("السجل_الاولي", conn, if_exists='append', index=False)
            
            # معالجة البيانات
            self.importProgressUpdated.emit(60, "جاري معالجة البيانات...")
            self.process_data_silently(cursor, current_academic_year)
            
            # تحديث الأقسام المسندة في البنية التربوية
            self.importProgressUpdated.emit(85, "جاري تحديث الأقسام المسندة...")
            self.update_assigned_sections(cursor, current_academic_year)
            
            # حفظ التغييرات
            self.importProgressUpdated.emit(90, "جاري حفظ التغييرات...")
            conn.commit()
            conn.close()
            conn = None
            
            # عرض النتائج
            self.importProgressUpdated.emit(100, "تم الانتهاء بنجاح!")
            self.emit_log(f"✅ تم استيراد بيانات منظومة مسار بنجاح للسنة الدراسية {current_academic_year}", "success")
            
            # تحديث الإحصائيات
            self.statisticsUpdated.emit(self.getDatabaseStatistics())
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد البيانات: {str(e)}", "error")
            if conn:
                conn.close()
    
    def process_data_silently(self, cursor, academic_year):
        """معالجة البيانات بهدوء"""
        try:
            # تحديث بيانات المؤسسة
            self.update_school_info(cursor)
            
            # إنشاء وتحديث اللوائح
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS 'اللوائح' (
                    'السنة_الدراسية' TEXT, 
                    'القسم' TEXT, 
                    'المستوى' TEXT, 
                    'الرمز' TEXT, 
                    'رت' TEXT, 
                    'مجموع التلاميذ' INTEGER DEFAULT 0, 
                    PRIMARY KEY('السنة_الدراسية', 'الرمز')
                )
            """)
            
            # حذف البيانات القديمة للسنة الدراسية
            if academic_year:
                cursor.execute("DELETE FROM اللوائح WHERE السنة_الدراسية = ?", (academic_year,))
            
            # إضافة البيانات الجديدة
            cursor.execute("""
                INSERT OR IGNORE INTO "اللوائح" ("السنة_الدراسية", "القسم", "المستوى", "الرمز", "رت")
                SELECT "السنة الدراسية", "القسم", "المستوى", "Unnamed: 1", "Unnamed: 0"
                FROM "السجل_الاولي"
            """)
            
            # تنظيف البيانات
            cursor.execute("""
                DELETE FROM "اللوائح" 
                WHERE "الرمز" IS NULL OR TRIM("الرمز") = '' OR "الرمز" = 'الرمز'
            """)
            
            # تحديث مجموع التلاميذ
            if academic_year:
                cursor.execute("""
                    UPDATE "اللوائح" as l1
                    SET "مجموع التلاميذ" = (
                        SELECT COUNT(*)
                        FROM "اللوائح" AS l2
                        WHERE l2."القسم" = l1."القسم"
                        AND l2."السنة_الدراسية" = l1."السنة_الدراسية"
                    )
                    WHERE l1."السنة_الدراسية" = ?
                """, (academic_year,))
            
            # تحديث السجل العام والبنية التربوية
            self.update_structure_tables(cursor, academic_year)
            
        except Exception as e:
            raise Exception(f"حدث خطأ أثناء معالجة البيانات: {str(e)}")
    
    def update_school_info(self, cursor):
        """تحديث بيانات المؤسسة - تحديث الحقول الأساسية فقط مع الحفاظ على البيانات الأخرى"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS بيانات_المؤسسة (
                الأكاديمية TEXT,
                المديرية TEXT,
                الجماعة TEXT,
                المؤسسة TEXT,
                السنة_الدراسية TEXT,
                البلدة TEXT,
                المدير TEXT,
                الحارس_العام TEXT,
                السلك TEXT,
                رقم_الحراسة TEXT,
                رقم_التسجيل TEXT,
                الأسدس TEXT,
                ImagePath1 TEXT
            )
        """)
        
        # استخراج بيانات المؤسسة من السجل الأولي
        cursor.execute("SELECT * FROM 'السجل_الاولي' LIMIT 6")
        rows = cursor.fetchall()
        
        if len(rows) >= 6:
            # التحقق من وجود سجل في جدول بيانات_المؤسسة
            cursor.execute("SELECT COUNT(*) FROM بيانات_المؤسسة")
            record_exists = cursor.fetchone()[0] > 0
            
            if record_exists:
                # تحديث الحقول الأساسية فقط مع الحفاظ على البيانات الأخرى
                cursor.execute("""
                    UPDATE بيانات_المؤسسة 
                    SET الأكاديمية = ?, 
                        المديرية = ?, 
                        الجماعة = ?, 
                        المؤسسة = ?,
                        السنة_الدراسية = ?
                """, (
                    rows[3][2] if len(rows) > 3 else '',  # الأكاديمية
                    rows[4][2] if len(rows) > 4 else '',  # المديرية  
                    rows[3][6] if len(rows) > 3 else '',  # الجماعة
                    rows[4][6] if len(rows) > 4 else '',  # المؤسسة
                    rows[5][6] if len(rows) > 5 else ''   # السنة الدراسية
                ))
            else:
                # إدراج سجل جديد إذا لم يكن موجوداً
                cursor.execute("""
                    INSERT INTO بيانات_المؤسسة 
                    (الأكاديمية, المديرية, الجماعة, المؤسسة, السنة_الدراسية, الأسدس)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    rows[3][2] if len(rows) > 3 else '',  # الأكاديمية
                    rows[4][2] if len(rows) > 4 else '',  # المديرية  
                    rows[3][6] if len(rows) > 3 else '',  # الجماعة
                    rows[4][6] if len(rows) > 4 else '',  # المؤسسة
                    rows[5][6] if len(rows) > 5 else '',  # السنة الدراسية
                    'الأول'  # الأسدس (افتراضي للسجلات الجديدة)
                ))
    
    def update_structure_tables(self, cursor, academic_year):
        """تحديث جداول البنية التربوية والسجل العام"""
        # تحديث السجل العام
        cursor.execute("""
            INSERT OR IGNORE INTO "السجل_العام"
                ("الرمز", "الاسم_والنسب", "النوع", "تاريخ_الازدياد", "مكان_الازدياد")
            SELECT "Unnamed: 1",
                   "Unnamed: 2" || ' ' || "Unnamed: 3",
                   "Unnamed: 4",
                   "Unnamed: 5", 
                   "Unnamed: 6"
            FROM "السجل_الاولي"
            WHERE "Unnamed: 1" IS NOT NULL
              AND TRIM("Unnamed: 1") <> ''
              AND "Unnamed: 1" <> 'الرمز'
        """)
        
        # تحديث البنية التربوية
        cursor.execute("""
            INSERT OR IGNORE INTO "البنية_التربوية" ("السنة_الدراسية", "القسم", "المستوى")
            SELECT DISTINCT "السنة_الدراسية", "القسم", "المستوى"
            FROM "اللوائح"
        """)
        
        # إضافة عمود مجموع التلاميذ إذا لم يكن موجوداً
        try:
            cursor.execute('ALTER TABLE "البنية_التربوية" ADD COLUMN "مجموع_التلاميذ" INTEGER DEFAULT 0')
        except sqlite3.OperationalError:
            pass
        
        # تحديث مجموع التلاميذ في البنية التربوية
        if academic_year:
            cursor.execute("""
                UPDATE "البنية_التربوية"
                SET "مجموع_التلاميذ" = (
                    SELECT COUNT(*)
                    FROM "اللوائح" AS l
                    WHERE l."السنة_الدراسية" = "البنية_التربوية"."السنة_الدراسية"
                      AND l."القسم" = "البنية_التربوية"."القسم"
                      AND l."المستوى" = "البنية_التربوية"."المستوى"
                )
                WHERE "السنة_الدراسية" = ?
            """, (academic_year,))

    def update_assigned_sections(self, cursor, academic_year):
        """تحديث الأقسام المسندة في جدول البنية التربوية"""
        try:
            self.emit_log("🔄 جاري تحديث الأقسام المسندة في البنية التربوية...", "progress")
            
            # إضافة عمود الأقسام_المسندة إذا لم يكن موجوداً
            try:
                cursor.execute('ALTER TABLE "البنية_التربوية" ADD COLUMN "الأقسام_المسندة" TEXT DEFAULT NULL')
                self.emit_log("✅ تم إضافة عمود الأقسام_المسندة إلى جدول البنية_التربوية", "info")
            except sqlite3.OperationalError:
                # العمود موجود بالفعل
                self.emit_log("ℹ️ عمود الأقسام_المسندة موجود بالفعل في جدول البنية_التربوية", "info")
                pass
            
            # تحديث جميع السجلات في جدول البنية التربوية لتكون "حراسة رقم 1"
            if academic_year:
                # تحديث السجلات للسنة الدراسية المحددة فقط
                cursor.execute("""
                    UPDATE "البنية_التربوية"
                    SET "الأقسام_المسندة" = 'حراسة رقم 1'
                    WHERE "السنة_الدراسية" = ?
                """, (academic_year,))
                
                # إحصاء السجلات المحدّثة
                updated_count = cursor.rowcount
                self.emit_log(f"✅ تم تحديث {updated_count} سجل في البنية التربوية للسنة الدراسية {academic_year}", "success")
            else:
                # تحديث جميع السجلات إذا لم تكن السنة الدراسية محددة
                cursor.execute("""
                    UPDATE "البنية_التربوية"
                    SET "الأقسام_المسندة" = 'حراسة رقم 1'
                """)
                
                # إحصاء السجلات المحدّثة
                updated_count = cursor.rowcount
                self.emit_log(f"✅ تم تحديث {updated_count} سجل في البنية التربوية لجميع السنوات الدراسية", "success")
            
            self.emit_log("🎉 تم إنجاز تحديث الأقسام المسندة بنجاح", "success")
            
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث الأقسام المسندة: {str(e)}", "error")
            raise Exception(f"فشل في تحديث الأقسام المسندة: {str(e)}")

    @pyqtSlot()
    def selectTeachersFile(self):
        """اختيار ملف أسماء الأساتذة"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_path, _ = QFileDialog.getOpenFileName(
            None, "اختر ملف الأساتذة", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_path:
            self.importTeachersData(file_path)

    def importTeachersData(self, file_path):
        """استيراد أسماء الأساتذة والمواد"""
        self.emit_log("بدء استيراد أسماء الأساتذة...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير البيانات...")

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إنشاء جدول الأساتذة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS الأساتذة (
                    رقم_الأستاذ INTEGER PRIMARY KEY AUTOINCREMENT,
                    اسم_الأستاذ TEXT,
                    المادة TEXT,
                    الرمز TEXT
                )
            ''')

            # حذف البيانات السابقة
            self.importProgressUpdated.emit(20, "جاري حذف البيانات السابقة...")
            cursor.execute("DELETE FROM الأساتذة")

            # قراءة ملف Excel
            self.importProgressUpdated.emit(40, "جاري قراءة ملف Excel...")
            df = pd.read_excel(file_path, header=None)

            if df.empty:
                self.emit_log("الملف فارغ", "warning")
                return

            # معالجة البيانات
            self.importProgressUpdated.emit(60, "جاري معالجة البيانات...")
            df_selected = df[[1, 2, 3]].copy()  # B, C, D columns
            df_selected.columns = ['المادة', 'الرمز', 'اسم_الأستاذ']

            # ملء الفراغات في عمود المادة
            previous_subject = None
            for index, row in df_selected.iterrows():
                current_subject = row['المادة']
                if pd.isna(current_subject) or (isinstance(current_subject, str) and current_subject.strip() == ''):
                    if previous_subject is not None:
                        df_selected.at[index, 'المادة'] = previous_subject
                else:
                    previous_subject = current_subject

            # تنظيف البيانات
            df_selected = df_selected.dropna(subset=['اسم_الأستاذ'])
            df_selected = df_selected[~df_selected['اسم_الأستاذ'].str.contains('المجموع الاجمالي', na=False)]

            # إدراج البيانات
            self.importProgressUpdated.emit(80, "جاري إدراج البيانات...")
            records_inserted = 0

            for _, row in df_selected.iterrows():
                if row['اسم_الأستاذ'].strip() and row['المادة'].strip():
                    cursor.execute(
                        "INSERT INTO الأساتذة (اسم_الأستاذ, المادة, الرمز) VALUES (?, ?, ?)",
                        (row['اسم_الأستاذ'], row['المادة'], row['الرمز'])
                    )
                    records_inserted += 1

            conn.commit();
            conn.close();
            conn = None;

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد {records_inserted} أستاذ بنجاح", "success")
            self.statisticsUpdated.emit(self.getDatabaseStatistics())

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد الأساتذة: {str(e)}", "error")
            if conn:
                conn.close()

    @pyqtSlot()
    def selectSecretCodesFiles(self):
        """اختيار ملفات الرموز السرية"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        file_paths, _ = QFileDialog.getOpenFileNames(
            None, "اختر ملفات الرموز السرية", "", "Excel Files (*.xlsx *.xls)"
        )

        if file_paths:
            self.importSecretCodes(file_paths)

    def importSecretCodes(self, file_paths):
        """استيراد الرموز السرية من ملفات متعددة - مطابق للملف الأصلي"""
        self.emit_log(f"بدء استيراد الرموز السرية من {len(file_paths)} ملف...", "progress")
        self.importProgressUpdated.emit(0, "جاري تحضير قاعدة البيانات...")

        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # التحقق من وجود جدول الرمز_السري وحذف محتوياته
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='الرمز_السري'")
            if cursor.fetchone():
                # حذف كافة السجلات من الجدول
                self.emit_log("جاري حذف البيانات السابقة من جدول الرمز_السري...", "progress")
                cursor.execute("DELETE FROM الرمز_السري")
                self.emit_log("تم حذف البيانات السابقة بنجاح", "success")
            else:
                # إنشاء جدول الرمز_السري إذا لم يكن موجوداً
                self.emit_log("جاري إنشاء جدول الرمز_السري...", "progress")
                cursor.execute('''
                    CREATE TABLE الرمز_السري (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        الرمز TEXT UNIQUE,
                        الرمز_السري TEXT
                    )
                ''')
                self.emit_log("تم إنشاء جدول الرمز_السري بنجاح", "success")

            # التحقق من عدد الملفات المختارة
            if len(file_paths) > 100:
                self.emit_log("تم اختيار أكثر من 100 ملف. سيتم استيراد أول 100 ملف فقط.", "warning")
                file_paths = file_paths[:100]

            self.emit_log(f"تم اختيار {len(file_paths)} ملف للاستيراد", "info")

            total_records_imported = 0

            # معالجة كل ملف على حدة
            for file_index, file_path in enumerate(file_paths):
                try:
                    file_name = os.path.basename(file_path)
                    progress = int((file_index / len(file_paths)) * 80)
                    self.importProgressUpdated.emit(progress, f"جاري معالجة الملف {file_index + 1}/{len(file_paths)}: {file_name}")

                    # التحقق من اسم الملف إذا كان يحتوي على "export_InfoEleve"
                    if "export_InfoEleve" not in file_name:
                        warning_message = f"الملف {file_name} لا يحتوي على العبارة 'export_InfoEleve' في اسمه. قد لا يكون هذا ملف رموز سرية صحيح."
                        self.emit_log(warning_message, "warning")

                    # قراءة ملف Excel
                    df = pd.read_excel(file_path, header=None)

                    if df.empty:
                        self.emit_log(f"الملف {file_name} فارغ. تم تخطيه.", "warning")
                        continue

                    # التحقق من وجود الأعمدة المطلوبة
                    if df.shape[1] < 6:  # نحتاج على الأقل 6 أعمدة للوصول إلى العمود F
                        self.emit_log(f"الملف {file_name} لا يحتوي على العدد الكافي من الأعمدة. تم تخطيه.", "warning")
                        continue

                    # استخراج الأعمدة المطلوبة: B (index 1) للرمز، F (index 5) للرمز السري
                    codes_df = pd.DataFrame({
                        'الرمز': df.iloc[:, 1],       # العمود B (index 1)
                        'الرمز_السري': df.iloc[:, 5]  # العمود F (index 5)
                    })

                    # حذف الصفوف التي تحتوي على قيم فارغة في أي من العمودين
                    codes_df = codes_df.dropna()

                    # تنظيف البيانات وتحويلها إلى نصوص
                    codes_df['الرمز'] = codes_df['الرمز'].astype(str)
                    codes_df['الرمز_السري'] = codes_df['الرمز_السري'].astype(str)

                    # عداد السجلات المضافة من هذا الملف
                    file_records_added = 0

                    # إدراج السجلات في قاعدة البيانات
                    for _, row in codes_df.iterrows():
                        try:
                            student_code = row['الرمز'].strip()
                            secret_code = row['الرمز_السري'].strip()

                            # تخطي السجلات ذات القيم الفارغة أو القصيرة جداً
                            if not student_code or not secret_code or len(student_code) < 3 or len(secret_code) < 3:
                                continue

                            # استخدام INSERT OR REPLACE لتحديث السجلات الموجودة أو إدراج سجلات جديدة
                            cursor.execute(
                                "INSERT OR REPLACE INTO الرمز_السري (الرمز, الرمز_السري) VALUES (?, ?)",
                                (student_code, secret_code)
                            )
                            file_records_added += 1
                        except Exception as e:
                            # تسجيل الخطأ للتصحيح
                            self.emit_log(f"خطأ في إدراج السجل: {str(e)}", "warning")

                    total_records_imported += file_records_added
                    self.emit_log(f"تم استيراد {file_records_added} سجل من الملف {file_name}", "info")

                except Exception as e:
                    self.emit_log(f"خطأ في معالجة الملف {file_name}: {str(e)}", "error")

            # تحديث جدول السجل_العام بالرموز السرية المستوردة
            self.importProgressUpdated.emit(85, "جاري تحديث جدول السجل_العام...")
            self.emit_log("جاري تحديث جدول السجل_العام بالرموز السرية الجديدة...", "progress")

            try:
                # التحقق من وجود عمود الرمز_السري في جدول السجل_العام
                cursor.execute("PRAGMA table_info(السجل_العام)")
                columns = [col[1] for col in cursor.fetchall()]

                if "الرمز_السري" not in columns:
                    # إضافة عمود الرمز_السري إذا لم يكن موجوداً
                    cursor.execute("ALTER TABLE السجل_العام ADD COLUMN الرمز_السري TEXT")
                    self.emit_log("تم إضافة عمود الرمز_السري إلى جدول السجل_العام", "info")

                # تحديث جدول السجل_العام بقيم الرمز السري من جدول الرمز_السري
                cursor.execute("""
                    UPDATE السجل_العام
                    SET الرمز_السري = (
                        SELECT الرمز_السري.الرمز_السري
                        FROM الرمز_السري
                        WHERE الرمز_السري.الرمز = السجل_العام.الرمز
                    )
                    WHERE EXISTS (
                        SELECT 1
                        FROM الرمز_السري
                        WHERE الرمز_السري.الرمز = السجل_العام.الرمز
                    )
                """)

                # عدد السجلات التي تم تحديثها
                updated_records = cursor.rowcount
                self.emit_log(f"تم تحديث {updated_records} سجل في جدول السجل_العام", "success")

            except sqlite3.OperationalError as e:
                self.emit_log(f"خطأ في تحديث جدول السجل_العام: {str(e)}", "error")

            # حفظ التغييرات
            conn.commit()
            conn.close()
            conn = None

            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log(f"✅ تم استيراد {total_records_imported} رمز سري وتحديث السجل العام بنجاح", "success")
            self.statisticsUpdated.emit(self.getDatabaseStatistics())

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد الرموز السرية: {str(e)}", "error")
            if conn:
                conn.close()

    @pyqtSlot()
    def importArrivalsData(self):
        """استيراد بيانات الوافدين والمغادرين"""
        if not self.pandas_available:
            self.emit_log("خطأ: مكتبة pandas غير متوفرة", "error")
            return

        # فتح حوار اختيار ملفات متعددة
        file_paths, _ = QFileDialog.getOpenFileNames(
            None, "اختر ملفات Excel للوافدين والمغادرين (يمكن اختيار عدة ملفات)", "", 
            "Excel Files (*.xlsx *.xls);;All Files (*.*)"
        )

        if file_paths:
            self.processArrivalsFiles(file_paths)

    def processArrivalsFiles(self, file_paths):
        """معالجة ملفات الوافدين والمغادرين"""
        self.emit_log("بدء استيراد بيانات الوافدين والمغادرين...", "progress")
        self.emit_log(f"تم اختيار {len(file_paths)} ملف للاستيراد", "info")
        self.importProgressUpdated.emit(0, "بدء المعالجة...")

        # إنشاء كائن المستورد
        arrivals_importer = ArrivalsImporter(self.emit_log)
        
        conn = None
        try:
            # فتح اتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # التحقق من الجدول وإنشاؤه إذا لزم الأمر
            arrivals_importer.create_arrivals_departures_table(cursor)

            # عرض إحصائيات الجدول قبل الاستيراد
            cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين")
            records_before = cursor.fetchone()[0]
            if records_before > 0:
                self.emit_log(f"📊 عدد السجلات الموجودة في الجدول قبل الاستيراد: {records_before}", "info")

            # معالجة كل ملف
            total_records_imported = 0
            successful_files = 0
            
            for i, file_path in enumerate(file_paths):
                try:
                    file_name = os.path.basename(file_path)
                    progress = (i * 100) // len(file_paths)
                    self.importProgressUpdated.emit(progress, f"معالجة الملف: {file_name}")
                    self.emit_log(f"🔄 معالجة الملف {i+1}/{len(file_paths)}: {file_name}", "progress")
                    
                    # قراءة الملف بدون تحديد header لضمان قراءة صحيحة للخلايا المحددة
                    df = pd.read_excel(file_path, header=None)
                    
                    if df.empty:
                        self.emit_log(f"الملف {file_name} فارغ", "warning")
                        continue
                    
                    # إنشاء أسماء أعمدة افتراضية من A إلى I
                    excel_column_names = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I']
                    if len(df.columns) >= 9:
                        df.columns = excel_column_names[:len(df.columns)]
                    else:
                        # إضافة أعمدة فارغة إذا كان العدد أقل من 9
                        for col_idx in range(len(df.columns), 9):
                            df[excel_column_names[col_idx]] = ""
                        df.columns = excel_column_names

                    self.emit_log(f"أعمدة الملف بعد التنسيق: {list(df.columns)}", "info")
                    
                    # استخراج البيانات الخاصة
                    سنة_دراسية, ملاحظات, مستوى = arrivals_importer.extract_special_fields(df)
                    
                    # عرض البيانات المستخرجة
                    if سنة_دراسية:
                        self.emit_log(f"📅 السنة الدراسية المستخرجة: {سنة_دراسية}", "info")
                    if ملاحظات:
                        self.emit_log(f"📝 الملاحظات المستخرجة: {ملاحظات}", "info")
                    if مستوى:
                        self.emit_log(f"📚 المستوى المستخرج: {مستوى}", "info")
                    
                    # تحديد الأعمدة
                    column_mapping = arrivals_importer.identify_columns(df.columns, ملاحظات)
                    
                    if not column_mapping:
                        self.emit_log(f"فشل في تحديد أعمدة الملف: {file_name}", "error")
                        continue
                    
                    # تحضير البيانات للإدراج
                    records_data = []
                    start_row = 10  # البدء من الصف 11
                    df_subset = df.iloc[start_row:]
                    
                    for index, row in df_subset.iterrows():
                        try:
                            # استخراج البيانات من كل صف
                            رقم_التلميذ = str(row[column_mapping.get('رقم_التلميذ', '')]).strip() if column_mapping.get('رقم_التلميذ') and pd.notna(row[column_mapping.get('رقم_التلميذ', '')]) else ""
                            النسب = str(row[column_mapping.get('النسب', '')]).strip() if column_mapping.get('النسب') and pd.notna(row[column_mapping.get('النسب', '')]) else ""
                            الإسم = str(row[column_mapping.get('الإسم', '')]).strip() if column_mapping.get('الإسم') and pd.notna(row[column_mapping.get('الإسم', '')]) else ""
                            
                            # معالجة التاريخ
                            تاريخ_التحويل = ""
                            if column_mapping.get('تاريخ_التحويل'):
                                date_value = row[column_mapping.get('تاريخ_التحويل')]
                                if pd.notna(date_value):
                                    if isinstance(date_value, str):
                                        تاريخ_التحويل = date_value.strip()
                                    else:
                                        try:
                                            تاريخ_التحويل = pd.to_datetime(date_value).strftime('%Y-%m-%d')
                                        except:
                                            تاريخ_التحويل = str(date_value).strip()
                            
                            نوع_التحويل = str(row[column_mapping.get('نوع_التحويل', '')]).strip() if column_mapping.get('نوع_التحويل') and pd.notna(row[column_mapping.get('نوع_التحويل', '')]) else ""
                            مؤسسة_الإستقبال = str(row[column_mapping.get('مؤسسة_الإستقبال', '')]).strip() if column_mapping.get('مؤسسة_الإستقبال') and pd.notna(row[column_mapping.get('مؤسسة_الإستقبال', '')]) else ""
                            المؤسسة_الأصلية = str(row[column_mapping.get('المؤسسة_الأصلية', '')]).strip() if column_mapping.get('المؤسسة_الأصلية') and pd.notna(row[column_mapping.get('المؤسسة_الأصلية', '')]) else ""
                            المديرية_الإقليمية_الأصلية = str(row[column_mapping.get('المديرية_الإقليمية_الأصلية', '')]).strip() if column_mapping.get('المديرية_الإقليمية_الأصلية') and pd.notna(row[column_mapping.get('المديرية_الإقليمية_الأصلية', '')]) else ""
                            الأكاديمية_الأصلية = str(row[column_mapping.get('الأكاديمية_الأصلية', '')]).strip() if column_mapping.get('الأكاديمية_الأصلية') and pd.notna(row[column_mapping.get('الأكاديمية_الأصلية', '')]) else ""
                            
                            # التحقق من وجود البيانات الأساسية
                            if الإسم and len(الإسم) > 1:
                                تاريخ_الآن = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
                                records_data.append((
                                    رقم_التلميذ, النسب, الإسم, تاريخ_التحويل, نوع_التحويل,
                                    مؤسسة_الإستقبال, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية,
                                    الأكاديمية_الأصلية, مستوى, سنة_دراسية, ملاحظات,
                                    file_name, تاريخ_الآن
                                ))
                        except Exception as row_error:
                            continue
                    
                    # إدراج البيانات في قاعدة البيانات
                    if records_data:
                        cursor.executemany("""
                            INSERT OR IGNORE INTO سجلات_الوافدين_والمغادرين 
                            (رقم_التلميذ, النسب, الإسم, تاريخ_التحويل, نوع_التحويل,
                             مؤسسة_الإستقبال, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية,
                             الأكاديمية_الأصلية, المستوى, السنة_الدراسية, ملاحظات,
                             اسم_الملف, تاريخ_الاستيراد)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, records_data)
                        
                        imported_count = cursor.rowcount
                        total_records_imported += imported_count
                        successful_files += 1
                        
                        self.emit_log(f"✅ تم استيراد {imported_count} سجل من الملف: {file_name}", "success")
                    
                except Exception as file_error:
                    self.emit_log(f"❌ خطأ في معالجة الملف {file_name}: {str(file_error)}", "error")
                    continue
            
            # حفظ التغييرات
            conn.commit()
            
            # عرض إحصائيات النهاية
            cursor.execute("SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين")
            records_after = cursor.fetchone()[0]
            
            self.importProgressUpdated.emit(100, "تم الانتهاء!")
            self.emit_log("📊 ملخص عملية الاستيراد:", "info")
            self.emit_log(f"• إجمالي الملفات المعالجة: {len(file_paths)}", "info")
            self.emit_log(f"• الملفات التي تم استيرادها بنجاح: {successful_files}", "info")
            self.emit_log(f"• السجلات المضافة في هذه العملية: {total_records_imported}", "info")
            if records_before > 0:
                self.emit_log(f"• السجلات الموجودة سابقاً: {records_before}", "info")
            self.emit_log(f"• إجمالي السجلات في الجدول الآن: {records_after}", "info")
            
            if successful_files > 0:
                self.emit_log("✅ تم استيراد بيانات الوافدين والمغادرين بنجاح!", "success")
                
                # تحديث بيانات الأكاديميات بعد الاستيراد
                self.emit_log("="*60, "info")
                self.emit_log("🔄 بدء مرحلة تحديث البيانات...", "progress")
                
                # إعادة فتح الاتصال للتحديث
                conn_update = get_database_connection()
                if conn_update:
                    cursor_update = conn_update.cursor()
                    
                    # تحديث الأكاديميات
                    arrivals_importer.update_academies_data(cursor_update)
                    
                    # حفظ التغييرات
                    conn_update.commit()
                    conn_update.close()
                
                self.emit_log("="*60, "info")
                self.emit_log("🎉 تم الانتهاء من جميع العمليات بنجاح!", "success")
                
                # تحديث الإحصائيات بعد الاستيراد
                self.refreshData()
            else:
                self.emit_log("⚠️ لم يتم استيراد أي ملف بنجاح", "warning")

        except Exception as e:
            self.emit_log(f"❌ خطأ في استيراد بيانات الوافدين والمغادرين: {str(e)}", "error")
        finally:
            if conn:
                conn.close()

    @pyqtSlot()
    def refreshData(self):
        """تحديث البيانات والإحصائيات"""
        self.emit_log("🔄 جاري تحديث البيانات...", "progress")
        try:
            # تحديث الإحصائيات
            self.statisticsUpdated.emit(self.getDatabaseStatistics())
            self.emit_log("✅ تم تحديث البيانات بنجاح", "success")
        except Exception as e:
            self.emit_log(f"❌ خطأ في تحديث البيانات: {str(e)}", "error")

    @pyqtSlot()
    def showHelpGuide(self):
        """عرض دليل المساعدة"""
        self.emit_log("📖 عرض دليل المساعدة...", "info")
        help_text = """
        🔹 استيراد اللوائح من منظومة مسار: لاستيراد بيانات التلاميذ من ملفات Excel
        🔹 استيراد الرمز السري: لاستيراد الرموز السرية للتلاميذ
        🔹 استيراد أسماء الأساتذة: لاستيراد بيانات الأساتذة والمواد
        🔹 استيراد الوافدين والمغادرين: لاستيراد بيانات التلاميذ الوافدين والمغادرين
        """
        self.emit_log(help_text, "info")

    @pyqtSlot()
    def returnToMainWindow(self):
        """العودة للنافذة الرئيسية"""
        self.emit_log("🔙 العودة للنافذة الرئيسية...", "info")
        self.returnToMain.emit()

    @pyqtSlot()
    def closeWindow(self):
        """إغلاق النافذة فقط"""
        self.close()

    def get_html_interface(self):
        """إنشاء واجهة HTML التفاعلية"""
        return """<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام استيراد البيانات الحديث</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Calibri', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            direction: rtl;
            overflow: hidden;
        }

        /* إخفاء أشرطة التمرير */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* Internet Explorer 10+ */
        }

        *::-webkit-scrollbar {
            width: 0;
            height: 0;
            background: transparent; /* Chrome/Safari/Webkit */
        }

        *::-webkit-scrollbar-track {
            background: transparent;
        }

        *::-webkit-scrollbar-thumb {
            background: transparent;
        }

        html {
            overflow: hidden;
        }

        .container {
            max-width: 95%;
            margin: 15px auto;
            display: grid;
            grid-template-rows: auto auto 1fr;
            gap: 15px;
            height: calc(100vh - 30px);
            overflow: hidden;
        }

        /* شريط العنوان */
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 30px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #667eea;
            font-size: 24px;
            margin: 0;
        }



        .header p {
            color: #666;
            font-size: 14px;
        }

        /* أزرار الاستيراد */
        .import-buttons {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .buttons-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .import-button {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-family: 'Calibri', sans-serif;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .import-button:hover {
            background: linear-gradient(135deg, #8e44ad, #7d3c98);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);
        }

        .import-button:active {
            transform: translateY(0);
        }

        /* منطقة شريط التحميل الرئيسي */
        .loading-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .loading-header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }

        .loading-title {
            color: #667eea;
            font-size: 20px;
            font-weight: bold;
        }

        .action-buttons {
            /* تم حذف الأزرار الصغيرة */
        }

        .action-button {
            /* تم حذف الأزرار الصغيرة */
        }

        .help-button {
            /* تم حذف الأزرار الصغيرة */
        }

        /* شريط التحميل الرئيسي */
        .main-progress-container {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 18px;
            margin-bottom: 15px;
            text-align: center;
        }

        .main-progress-bar {
            width: 100%;
            height: 30px;
            background: #ecf0f1;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .main-progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.4s ease;
            border-radius: 15px;
            position: relative;
        }

        .main-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, 
                rgba(255,255,255,0.2) 25%, 
                transparent 25%, 
                transparent 50%, 
                rgba(255,255,255,0.2) 50%, 
                rgba(255,255,255,0.2) 75%, 
                transparent 75%);
            background-size: 20px 20px;
            animation: progressAnimation 1s linear infinite;
        }

        @keyframes progressAnimation {
            0% { background-position: 0 0; }
            100% { background-position: 20px 0; }
        }

        .main-progress-text {
            color: #2c3e50;
            font-weight: bold;
            font-size: 20px;
            margin-bottom: 10px;
        }

        .main-progress-percentage {
            color: #667eea;
            font-size: 24px;
            font-weight: bold;
        }

        .status-message {
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid #3498db;
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            color: #2c3e50;
            font-weight: bold;
        }

        /* شريط التقدم */
        .progress-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            min-width: 400px;
            z-index: 1000;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 25px;
            background: #ecf0f1;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.4s ease;
            border-radius: 12px;
        }

        .progress-text {
            text-align: center;
            color: #2c3e50;
            font-weight: bold;
            font-size: 20px;
        }

        /* إحصائيات سريعة */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-top: 10px;
        }

        .stat-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            font-size: 20px;
        }

        .stat-value {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #ffffff;
        }

        .stat-label {
            font-family: 'Calibri', Arial, sans-serif;
            font-size: 20px;
            font-weight: bold;
            opacity: 0.9;
            color: #ffffff;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- شريط العنوان -->
        <div class="header">
            <div class="header-content">
                <h1>🚀 استيراد البيانات وتحيينها</h1>
            </div>
        </div>

        <!-- أزرار الاستيراد -->
        <div class="import-buttons">
            <div class="buttons-grid">
                <button class="import-button" onclick="importMasarData()">
                    📚 استيراد اللوائح من منظومة مسار باللغة العربية
                </button>
                <button class="import-button" onclick="importSecretCodes()">
                    🔐 استيراد الرمز السري وتحيينه دفعة واحدة
                </button>
                <button class="import-button" onclick="importTeachers()">
                    👨‍🏫 استيراد أسماء الأساتذة والمواد المدرسة
                </button>
                <button class="import-button" onclick="importArrivals()">
                    🔄 استيراد بيانات الوافدين والمغادرين
                </button>
            </div>
        </div>

        <!-- منطقة شريط التحميل -->
        <div class="loading-panel">
            <div class="loading-header">
                <div class="loading-title"> حالة التحميل والإحصائيات</div>
                <div class="action-buttons">
                    <!-- تم حذف الأزرار الصغيرة -->
                </div>
            </div>

            <!-- شريط التحميل الرئيسي -->
            <div class="main-progress-container" id="mainProgressContainer">
                <div class="main-progress-text" id="mainProgressText">جاهز للاستخدام</div>
                <div class="main-progress-bar">
                    <div class="main-progress-fill" id="mainProgressFill" style="width: 0%;"></div>
                </div>
                <div class="main-progress-percentage" id="mainProgressPercentage">0%</div>
                <div class="status-message" id="statusMessage" style="display: none;">
                    جاري المعالجة...
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-container" id="statsContainer">
                <div class="stat-item">
                    <div class="stat-value" id="levelsCount">0</div>
                    <div class="stat-label">عدد المستويات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="sectionsCount">0</div>
                    <div class="stat-label">عدد الأقسام</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="studentsCount">0</div>
                    <div class="stat-label">عدد التلاميذ</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="teachersCount">0</div>
                    <div class="stat-label">الأساتذة</div>
                </div>
            </div>
        </div>
    </div>

    <!-- شريط التقدم -->
    <div class="progress-container" id="progressContainer">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="progress-text" id="progressText">جاري المعالجة...</div>
    </div>

    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let dataEngine = null;
        let isChannelReady = false;

        // إعداد قناة التواصل مع Python
        function initializeChannel() {
            if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                new QWebChannel(qt.webChannelTransport, function(channel) {
                    dataEngine = channel.objects.dataEngine;
                    isChannelReady = true;

                    console.log('🚀 QWebChannel initialized successfully');

                    // ربط الإشارات
                    if (dataEngine) {
                        dataEngine.logUpdated.connect(addStatusMessage);
                        dataEngine.importProgressUpdated.connect(updateProgress);
                        dataEngine.statisticsUpdated.connect(updateStatistics);

                        // تحميل الإحصائيات عند البدء
                        loadStatistics();
                        checkPandasAvailability();

                        // رسالة ترحيبية
                        addStatusMessage('أهلاً بك في نظام استيراد البيانات وتحيينها - النظام جاهز للاستخدام', 'success');
                    }
                });
            } else {
                console.log('⚠️ QWebChannel not available, retrying...');
                setTimeout(initializeChannel, 100);
            }
        }

        // إضافة رسالة حالة
        function addStatusMessage(message, status) {
            const statusMessage = document.getElementById('statusMessage');
            const mainProgressText = document.getElementById('mainProgressText');
            
            if (statusMessage && mainProgressText) {
                statusMessage.style.display = 'block';
                statusMessage.textContent = message;
                mainProgressText.textContent = status === 'success' ? '✅ تم بنجاح' : 
                                              status === 'error' ? '❌ خطأ' :
                                              status === 'warning' ? '⚠️ تحذير' : 
                                              '🔄 جاري المعالجة';
                
                // إخفاء الرسالة بعد 5 ثوان للرسائل الناجحة
                if (status === 'success') {
                    setTimeout(() => {
                        statusMessage.style.display = 'none';
                        mainProgressText.textContent = 'جاهز للاستخدام';
                    }, 5000);
                }
            }
        }

        // تحديث شريط التقدم الرئيسي
        function updateMainProgress(percentage, statusText) {
            const mainProgressFill = document.getElementById('mainProgressFill');
            const mainProgressPercentage = document.getElementById('mainProgressPercentage');
            const mainProgressText = document.getElementById('mainProgressText');
            const statusMessage = document.getElementById('statusMessage');

            if (mainProgressFill && mainProgressPercentage && mainProgressText) {
                mainProgressFill.style.width = percentage + '%';
                mainProgressPercentage.textContent = percentage + '%';
                mainProgressText.textContent = statusText;
                
                if (percentage > 0 && percentage < 100) {
                    statusMessage.style.display = 'block';
                    statusMessage.textContent = statusText;
                } else if (percentage >= 100) {
                    setTimeout(() => {
                        mainProgressFill.style.width = '0%';
                        mainProgressPercentage.textContent = '0%';
                        mainProgressText.textContent = 'جاهز للاستخدام';
                        statusMessage.style.display = 'none';
                    }, 2000);
                }
            }
        }

        // تحديث شريط التقدم
        function updateProgress(percentage, statusText) {
            updateMainProgress(percentage, statusText);
            
            // تحديث شريط التقدم المنبثق أيضاً للتوافق
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            if (percentage > 0 && percentage < 100) {
                progressContainer.style.display = 'block';
                progressFill.style.width = percentage + '%';
                progressText.textContent = statusText;
            } else if (percentage >= 100) {
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 1000);
            }
        }

        // تحديث الإحصائيات
        function updateStatistics(statsJson) {
            try {
                const stats = JSON.parse(statsJson);

                document.getElementById('levelsCount').textContent = stats.levels_count || 0;
                document.getElementById('sectionsCount').textContent = stats.sections_count || 0;
                document.getElementById('studentsCount').textContent = stats.students_count || 0;
                document.getElementById('teachersCount').textContent = stats.teachers_count || 0;

            } catch (error) {
                console.error('خطأ في تحليل الإحصائيات:', error);
            }
        }

        // تحميل الإحصائيات
        function loadStatistics() {
            if (dataEngine) {
                addStatusMessage('جاري تحديث الإحصائيات...', 'progress');
                const stats = dataEngine.getDatabaseStatistics();
                updateStatistics(stats);
                addStatusMessage('تم تحديث الإحصائيات بنجاح', 'success');
            }
        }

        // التحقق من توفر pandas
        function checkPandasAvailability() {
            if (dataEngine) {
                const available = dataEngine.checkPandasAvailability();
                if (!available) {
                    addStatusMessage('تحذير: مكتبة pandas غير متوفرة. لن تعمل وظائف استيراد البيانات من Excel. لتثبيت pandas، استخدم الأمر: pip install pandas', 'warning');
                } else {
                    addStatusMessage('مكتبة pandas متوفرة ومجهزة للاستخدام', 'success');
                }
            }
        }

        // وظائف الاستيراد
        function importMasarData() {
            if (dataEngine) {
                addStatusMessage('بدء استيراد بيانات منظومة مسار...', 'progress');
                dataEngine.selectMasarFile();
            } else {
                addStatusMessage('خطأ: النظام غير جاهز بعد', 'error');
            }
        }

        function importTeachers() {
            if (dataEngine) {
                addStatusMessage('بدء استيراد أسماء الأساتذة...', 'progress');
                dataEngine.selectTeachersFile();
            } else {
                addStatusMessage('خطأ: النظام غير جاهز بعد', 'error');
            }
        }

        function importSecretCodes() {
            if (dataEngine) {
                addStatusMessage('بدء استيراد الرموز السرية...', 'progress');
                dataEngine.selectSecretCodesFiles();
            } else {
                addStatusMessage('خطأ: النظام غير جاهز بعد', 'error');
            }
        }

        function importArrivals() {
            if (dataEngine) {
                addStatusMessage('بدء استيراد بيانات الوافدين والمغادرين...', 'progress');
                dataEngine.importArrivalsData();
            } else {
                addStatusMessage('خطأ: النظام غير جاهز بعد', 'error');
            }
        }

        function refreshData() {
            // دالة محذوفة - لم تعد مطلوبة
        }

        function showHelp() {
            // دالة محذوفة - لم تعد مطلوبة
        }

        function returnToMain() {
            // دالة محذوفة - لم تعد مطلوبة
        }

        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 تحميل نظام استيراد البيانات وتحيينها...');
            initializeChannel();
        });

        console.log('🚀 تم تحميل واجهة استيراد البيانات وتحيينها بنجاح!');
    </script>
</body>
</html>
        """

class ModernSub1Window(QMainWindow):
    """النافذة الرئيسية لاستيراد البيانات وتحيينها - للاستخدام المستقل"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("استيراد البيانات وتحيينها")
        self.setWindowIcon(QIcon())
        self.resize(1100, 650)
        
        # إعداد النافذة كمشروطة (Modal) مع زر الإغلاق فقط
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowTitleHint)
        self.setWindowModality(Qt.ApplicationModal)
        
        # تمركز النافذة في وسط الشاشة
        self.center_window()
        
        self.dataEngine = DataImportEngine()

        # إعداد QWebEngineView
        self.webView = QWebEngineView(self)
        self.setCentralWidget(self.webView)

        # إعداد QWebChannel
        self.channel = QWebChannel()
        self.channel.registerObject('dataEngine', self.dataEngine)
        self.channel.registerObject('importWindow', self)
        self.webView.page().setWebChannel(self.channel)

        # تحميل واجهة HTML
        html = self.dataEngine.get_html_interface()
        self.webView.setHtml(html, QUrl("qrc:///"))

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget()
        screen_rect = desktop.screenGeometry()
        window_rect = self.geometry()
        x = (screen_rect.width() - window_rect.width()) // 2
        y = (screen_rect.height() - window_rect.height()) // 2
        self.move(x, y)

    @pyqtSlot()
    def closeWindow(self):
        """إغلاق النافذة فقط وليس البرنامج"""
        self.close()

def main():
    """تشغيل التطبيق"""
    app = QApplication(sys.argv)

    # إعداد التطبيق
    app.setApplicationName("استيراد البيانات وتحيينها")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Modern Education Systems")

    # إنشاء النافذة الرئيسية
    window = ModernSub1Window()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
