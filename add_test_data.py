#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
from database_config import get_database_path

def add_test_data():
    """إضافة بيانات اختبارية للاختبار"""
    try:
        conn = sqlite3.connect(get_database_path())
        cursor = conn.cursor()
        
        # إضافة سجلات اختبارية
        cursor.execute('''
            INSERT INTO سجلات_الوافدين_والمغادرين 
            (رقم_التلميذ, النسب, الإسم, المستوى, تاريخ_التحويل, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية, ملاحظات, السنة_الدراسية)
            VALUES 
            ('12345', 'احمد', 'علي', 'السادس', '2024-09-01', 'مدرسة الاختبار', 'الدار البيضاء', 'لائحة التحويلات (الوافدون)', '2024/2025'),
            ('12346', 'فاطمة', 'محمد', 'الخامس', '2024-09-02', 'مدرسة النجاح', 'الرباط', 'لائحة التحويلات (الوافدون)', '2024/2025'),
            ('12347', 'يوسف', 'أحمد', 'الرابع', '2024-09-03', 'مدرسة المستقبل', 'فاس', 'لائحة التحويلات (الوافدون)', '2024/2025')
        ''')
        
        conn.commit()
        count = cursor.rowcount
        conn.close()
        
        print(f'تم إضافة {count} سجلات اختبارية بنجاح')
        return count
        
    except Exception as e:
        print(f'خطأ في إضافة البيانات الاختبارية: {e}')
        return 0

if __name__ == "__main__":
    add_test_data()