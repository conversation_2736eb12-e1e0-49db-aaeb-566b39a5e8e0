"""
اختبار سريع لنافذة الوافدين والمغادرين مع نظام التصفية المطور
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# إضافة مسار المجلد الحالي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sub199_window_html import Sub199Window

def test_arrivals_departures_window():
    print("🚀 بدء اختبار نافذة الوافدين والمغادرين...")
    
    app = QApplication(sys.argv)
    
    try:
        # إنشاء النافذة
        window = Sub199Window()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم فتح النافذة بنجاح!")
        print("📋 يجب أن تظهر قائمة بسجلات الوافدين (افتراضي)")
        print("🔍 اختبر التصفية: اختر 'المغادرون' من قائمة التصفية")
        print("📊 راقب تغيير عمود 'المؤسسة الأصلية' إلى 'مؤسسة الاستقبال'")
        print("🖨️ اختبر زر الطباعة للتأكد من عمل التقرير")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النافذة: {e}")
        return False

if __name__ == "__main__":
    test_arrivals_departures_window()