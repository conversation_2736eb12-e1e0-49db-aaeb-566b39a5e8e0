import sqlite3
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QMessageBox, QInputDialog, QFrame, QLabel,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy,
                            QLineEdit, QGridLayout, QFileDialog, QProgressDialog,
                            QApplication, QDialog, QRadioButton, QButtonGroup)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDateTime
import os
import shutil
import zipfile
import datetime
import tempfile
import sys
import hashlib

# تم إزالة استيراد مكتبات Access لأنها لم تعد مطلوبة
# البرنامج يستخدم الآن ملفات Excel بدلاً من Access

# تعريف متغيرات للتوافق مع الكود القديم
PYODBC_AVAILABLE = False
PANDAS_ACCESS_AVAILABLE = False
print("تم تعطيل دعم ملفات Access. البرنامج يستخدم الآن ملفات Excel فقط.")

# استيراد pandas كبديل للتعامل مع ملفات Excel
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# استيراد xlrd و openpyxl للتعامل المباشر مع ملفات Excel
try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

class Sub8Window(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(" إعدادات البرنامج ")
        
        # إعداد النافذة كمشروطة مع زر الإغلاق الأساسي فقط
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowTitleHint)
        self.setWindowModality(Qt.ApplicationModal)  # جعل النافذة مشروطة
        
        # تعيين الحجم الثابت
        self.setFixedSize(900, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setStyleSheet("""
            background-color: #f5f5f5;
            /* إخفاء أشرطة التمرير العمودية */
            QScrollBar:vertical {
                width: 0px;
                background: transparent;
            }
            QScrollArea {
                border: none;
            }
            QScrollArea > QWidget > QWidget {
                background: transparent;
            }
        """)  # لون خلفية خفيف وإخفاء أشرطة التمرير

        # تعريف مسار قاعدة البيانات باستخدام النظام الجديد
        self.db_path = get_database_path()

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)        # إنشاء عنوان الصفحة بتصميم أكبر وأكثر وضوحًا
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #0D47A1;  /* لون أزرق غامق */
                border-radius: 12px;
                min-height: 80px;  /* زيادة ارتفاع الإطار */
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)  # زيادة الهوامش

        # إضافة أيقونة للعنوان بحجم أكبر
        title_label = QLabel("⚙️   إعدادات البرنامج ")
        title_label.setFont(QFont("Amiri", 22, QFont.Bold))  # زيادة حجم الخط
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # إضافة تأثير الظل للعنوان
        self.apply_shadow(title_frame)

        # إضافة العنوان للتخطيط الرئيسي
        main_layout.addWidget(title_frame)

        # إنشاء إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
            }
        """)

        # إضافة تأثير الظل للإطار
        self.apply_shadow(buttons_frame)

        # تخطيط الأزرار
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(15)  # تقليل المسافة بين الأزرار

        # تعديل التخطيط ليكون شبكي بدلاً من عمودي
        grid_layout = QGridLayout()
        grid_layout.setHorizontalSpacing(15)
        grid_layout.setVerticalSpacing(15)
        buttons_layout.addLayout(grid_layout)

        # إنشاء الأزرار مع إضافة الأزرار الجديدة للحماية
        button_data = [
            ("🗑️ حذف جميع البيانات", "#e74c3c", self.delete_all_data),  # أحمر
            ("🔄 تهيئة البرنامج لبداية سنة دراسية جديدة", "#ff9800", self.reset_school_year),  # برتقالي
            ("🛡️ تأمين مجلد البرنامج برمز سري", "#6f42c1", self.secure_program_folder),  # بنفسجي غامق
            ("🔓 إلغاء حماية مجلد محمي", "#fd7e14", self.remove_folder_protection),  # برتقالي محمر
            ("💾 نسخ احتياطي للبيانات", "#f39c12", self.backup_database),  # برتقالي فاتح
            ("📂 استيراد نسخة احتياطية", "#9b59b6", self.restore_backup),  # بنفسجي
            ("📊 إحصائيات قاعدة البيانات", "#1abc9c", self.show_database_statistics),  # فيروزي
            ("🔒 استيراد أرقام الهواتف من النسخة السابقة", "#2ecc71", self.import_phone_numbers),  # أخضر
            ("📝 إدراج الغياب الأسبوعي", "#27ae60", self.insert_weekly_absence)  # أخضر غامق
        ]        # تنظيم الأزرار في شبكة (2 عمود و 5 صفوف)
        self.buttons = []
        for i, (text, color, handler) in enumerate(button_data):
            btn = QPushButton(text)
            btn.setFont(QFont("Calibri", 14, QFont.Bold))  # تقليل حجم الخط قليلاً لاستيعاب النص الأطول
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px;
                    text-align: right;
                    min-height: 45px;
                    min-width: 220px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                    transform: translateY(-2px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, factor=30)};
                    transform: translateY(0px);
                }}
            """)
            btn.clicked.connect(handler)

            # ترتيب الأزرار في 2 عمود
            row = i // 2
            col = i % 2
            grid_layout.addWidget(btn, row, col)
            self.buttons.append(btn)

        # إضافة مساحة مرنة في نهاية تخطيط الأزرار
        buttons_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # إضافة إطار الأزرار للتخطيط الرئيسي
        main_layout.addWidget(buttons_frame)

    def apply_shadow(self, widget):
        """تطبيق تأثير الظل على العنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(3)
        shadow.setYOffset(3)
        shadow.setColor(QColor(0, 0, 0, 60))
        widget.setGraphicsEffect(shadow)

    def darken_color(self, color, factor=15):
        """تغميق اللون بنسبة معينة"""
        # تحويل اللون من تنسيق hex إلى RGB
        color = color.lstrip('#')
        r, g, b = int(color[0:2], 16), int(color[2:4], 16), int(color[4:6], 16)

        # تقليل قيم RGB بنسبة factor
        r = max(0, r - factor)
        g = max(0, g - factor)
        b = max(0, b - factor)        # إعادة تحويل اللون إلى تنسيق hex
        return f"#{r:02x}{g:02x}{b:02x}"

    def closeEvent(self, event):
        """إغلاق النافذة فقط بدون إنهاء البرنامج"""
        # السماح بإغلاق النافذة مباشرة
        event.accept()

    def delete_all_data(self):
        """حذف جميع البيانات مع التأكد من كلمة المرور"""
        # إنشاء مربع حوار لإدخال كلمة المرور
        password_dialog = QInputDialog(self)
        password_dialog.setWindowTitle("التحقق من الهوية")
        password_dialog.setLabelText("الرجاء إدخال رمز الحذف للمتابعة:")
        password_dialog.setTextEchoMode(QLineEdit.Password)
        password_dialog.setStyleSheet("""
            QInputDialog {
                background-color: white;
            }            QLabel {
                font-family: 'Calibri';
                font-size: 15pt;
                font-weight: bold;
                color: black;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-family: 'Calibri';
                font-size: 15pt;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 15pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)

        ok = password_dialog.exec_()
        password = password_dialog.textValue()

        # التحقق من صحة كلمة المرور
        if ok and password == "12345":
            # تنفيذ عملية الحذف فورا
            self.perform_deletion()
        else:
            if ok:  # إذا ضغط المستخدم على "موافق" لكن كلمة المرور خاطئة
                self.show_status_message("رمز الحذف غير صحيح!", "error")

    def reset_school_year(self):
        """تهيئة البرنامج لبداية سنة دراسية جديدة من خلال حذف بيانات محددة"""
        # إنشاء مربع حوار للتأكيد
        confirm_dialog = QMessageBox(self)
        confirm_dialog.setWindowTitle("تأكيد التهيئة")
        confirm_dialog.setText("هل أنت متأكد من تهيئة البرنامج لسنة دراسية جديدة؟")
        confirm_dialog.setInformativeText("")
        confirm_dialog.setIcon(QMessageBox.Warning)
        confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        confirm_dialog.button(QMessageBox.Yes).setText("نعم")
        confirm_dialog.button(QMessageBox.No).setText("لا")
        confirm_dialog.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)

        # تنفيذ التهيئة إذا تم النقر على نعم
        if confirm_dialog.exec_() == QMessageBox.Yes:
            try:
                # إنشاء مربع حوار لإدخال كلمة المرور للتأكيد
                password_dialog = QInputDialog(self)
                password_dialog.setWindowTitle("التحقق من الهوية")
                password_dialog.setLabelText("الرجاء إدخال رمز التأكيد للمتابعة:")
                password_dialog.setTextEchoMode(QLineEdit.Password)
                password_dialog.setStyleSheet("""
                    QInputDialog {
                        background-color: white;
                    }
                    QLabel {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        color: black;
                    }
                    QLineEdit {
                        padding: 8px;
                        border: 1px solid #bdc3c7;
                        border-radius: 5px;
                        font-family: 'Calibri';
                        font-size: 13pt;
                    }
                    QPushButton {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        min-width: 80px;
                        padding: 5px;
                    }
                """)

                ok = password_dialog.exec_()
                password = password_dialog.textValue()

                # التحقق من صحة كلمة المرور (نفس الرمز المستخدم لحذف جميع البيانات)
                if ok and password == "12345":
                    # تنفيذ عملية التهيئة
                    self.perform_reset()
                else:
                    if ok:  # إذا ضغط المستخدم على "موافق" لكن كلمة المرور خاطئة
                        self.show_status_message("رمز التأكيد غير صحيح!", "error")
            except Exception as e:
                self.show_status_message(f"خطأ أثناء التهيئة: {str(e)}", "error")

    def perform_reset(self):
        """تنفيذ عملية تهيئة البرنامج لبداية سنة دراسية جديدة"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # قائمة الجداول المراد تفريغها
            tables_to_clear = ['ورقة_السماح_بالدخول', 'تبريرات_الغياب', 'المخالفات', 'الشهادة_المدرسية', 'مسك_الغياب_الأسبوعي', 'مسك_أوراق_الفروض', 'زيارة_ولي_الأمر', 'جدول_عام', 'البنية_التربوية']
            tables_cleared = []

            # التحقق من وجود كل جدول قبل محاولة حذف البيانات منه
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM '{table}'")
                    tables_cleared.append(table)

            # إكمال العملية
            conn.commit()
            conn.close()

            # عرض رسالة تأكيد بالجداول التي تم تفريغها
            result_message = "تم تهيئة البرنامج لسنة دراسية جديدة بنجاح!"
            details = "تم حذف البيانات من الجداول التالية:\n"
            for table in tables_cleared:
                details += f"✓ {table}\n"

            self.show_status_message(f"{result_message}\n\n{details}", "success")

        except Exception as e:
            self.show_status_message(f"خطأ أثناء تهيئة البرنامج: {str(e)}", "error")

    def perform_deletion(self):
        """تنفيذ عملية حذف البيانات من الجداول المحددة"""
        try:
            # قائمة الجداول المراد حذف بياناتها
            tables_to_clear = [
                'البنية_التربوية',
                'جدول_عام',
                'زيارة_ولي_الأمر',
                'تبريرات_الغياب',
                'المخالفات',
                'الأساتذة',
                'السجل_العام',
                'اللوائح',
                'الشهادة_المدرسية',
                'ورقة_السماح_بالدخول',
                'اخبار_بنشاط',
                'الرمز_السري',
                'السجل_الاولي',
                'زيارات_أولياء_الأمور',
                'غياب_الأسدس_الأول',
                'غياب_الأسدس_الثاني',
                'مجموع_الغياب_السنوي',
                'مسك_أوراق_الفروض',
                'مسك_الغياب_الأسبوعي'
            ]

            # عرض رسالة تعليمات عادية لتأكيد الحذف
            instruction_dialog = QMessageBox(self)
            instruction_dialog.setWindowTitle("تأكيد الحذف")
            instruction_dialog.setText("تم حذف جميع البيانات بنجاح")

            # إنشاء نص تفصيلي للجداول التي تم حذفها
            deleted_tables_text = "تم مسح البيانات من الجداول التالية:\n"
            for table in tables_to_clear:
                deleted_tables_text += f"• {table}\n"
            deleted_tables_text += "\nمع الاحتفاظ بالسنة الدراسية في جدول بيانات_المؤسسة"
            deleted_tables_text += "\n\nسيتم إغلاق البرنامج الآن لإتمام عملية الحذف وضغط قاعدة البيانات."

            instruction_dialog.setInformativeText(deleted_tables_text)
            instruction_dialog.setIcon(QMessageBox.Information)
            instruction_dialog.setStandardButtons(QMessageBox.Ok)

            # إضافة أيقونة البرنامج إلى نافذة الرسالة
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                instruction_dialog.setWindowIcon(QIcon(icon_path))

            instruction_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: white;
                    background-color: #0D47A1;
                    border: none;
                    border-radius: 5px;
                    padding: 5px 15px;
                    min-width: 140px;
                }
                QPushButton:hover {
                    background-color: #1565C0;
                }
                QPushButton:pressed {
                    background-color: #0D47A1;
                }
            """)

            # تنفيذ حذف البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # التحقق من وجود كل جدول قبل محاولة حذف البيانات منه
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM {table}")

            # معالجة خاصة لجدول بيانات_المؤسسة - حذف جميع البيانات مع الاحتفاظ بالسنة_الدراسية
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if cursor.fetchone():
                # الحصول على السنة الدراسية الحالية
                cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                current_school_year = result[0] if result else None

                if current_school_year:
                    # الحصول على أسماء جميع الأعمدة في جدول بيانات_المؤسسة
                    cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                    columns = [column[1] for column in cursor.fetchall()]

                    # حذف جميع البيانات من الجدول
                    cursor.execute("DELETE FROM بيانات_المؤسسة")

                    # إنشاء استعلام INSERT مع تعيين جميع الأعمدة إلى سلاسل فارغة باستثناء السنة_الدراسية
                    column_names = ", ".join(columns)
                    placeholders = []
                    values = []

                    for column in columns:
                        if column == "السنة_الدراسية":
                            placeholders.append("?")
                            values.append(current_school_year)
                        else:
                            placeholders.append("?")
                            values.append("")  # سلسلة فارغة بدلاً من NULL

                    placeholders_str = ", ".join(placeholders)

                    # إعادة إدخال سجل جديد مع تعيين جميع الأعمدة إلى سلاسل فارغة باستثناء السنة_الدراسية
                    insert_query = f"INSERT INTO بيانات_المؤسسة ({column_names}) VALUES ({placeholders_str})"
                    cursor.execute(insert_query, values)

                    print(f"تم الاحتفاظ بالسنة الدراسية: {current_school_year} وتعيين باقي الحقول إلى سلاسل فارغة")
                else:
                    # إذا لم تكن هناك سنة دراسية، قم بحذف جميع البيانات
                    cursor.execute("DELETE FROM بيانات_المؤسسة")

            # التحقق من وجود جدول اللوائح وإنشائه إذا لم يكن موجوداً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اللوائح'")
            if not cursor.fetchone():
                # إنشاء جدول اللوائح إذا لم يكن موجوداً
                try:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS اللوائح (
                            السنة_الدراسية TEXT,
                            القسم TEXT,
                            المستوى TEXT,
                            الرمز TEXT,
                            رت TEXT,
                            مجموع التلاميذ INTEGER DEFAULT 0,
                            PRIMARY KEY(السنة_الدراسية, الرمز)
                        )
                    """)
                    print("تم إنشاء جدول اللوائح")
                except Exception as e:
                    print(f"خطأ في إنشاء جدول اللوائح: {e}")
                    try:
                        cursor.execute("""
                            CREATE TABLE IF NOT EXISTS [اللوائح] (
                                [السنة_الدراسية] TEXT,
                                [القسم] TEXT,
                                [المستوى] TEXT,
                                [الرمز] TEXT,
                                [رت] TEXT,
                                [مجموع التلاميذ] INTEGER DEFAULT 0,
                                PRIMARY KEY([السنة_الدراسية], [الرمز])
                            )
                        """)
                        print("تم إنشاء جدول اللوائح باستخدام الطريقة البديلة")
                    except Exception as e2:
                        print(f"فشل إنشاء جدول اللوائح: {e2}")

            # حذف وإضافة بيانات افتراضية لجدول اللوائح
            try:
                # حذف جميع البيانات من جدول اللوائح
                cursor.execute("DELETE FROM اللوائح")
            except Exception as e:
                print(f"خطأ في حذف بيانات جدول اللوائح: {e}")
                # محاولة بديلة لحذف البيانات
                try:
                    cursor.execute("DELETE FROM [اللوائح]")
                except Exception as e2:
                    print(f"فشلت المحاولة البديلة لحذف بيانات جدول اللوائح: {e2}")

            # إضافة سجل افتراضي لجدول اللوائح
            if current_school_year:
                    # التحقق من وجود جدول السجل_العام
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='السجل_العام'")
                    if cursor.fetchone():
                        # التحقق من وجود السجل في جدول السجل_العام
                        cursor.execute("SELECT COUNT(*) FROM السجل_العام WHERE الرمز = 'A12345678'")
                        if cursor.fetchone()[0] == 0:
                            # إضافة سجل افتراضي في جدول السجل_العام إذا لم يكن موجوداً
                            cursor.execute("""
                                INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                                VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                            """)
                            print("تم إضافة سجل افتراضي في جدول السجل_العام")
                    else:
                        # إنشاء جدول السجل_العام إذا لم يكن موجوداً
                        cursor.execute("""
                            CREATE TABLE IF NOT EXISTS 'السجل_العام' (
                                'الرمز' TEXT PRIMARY KEY,
                                'الاسم_والنسب' TEXT,
                                'السماح' TEXT,
                                'التأخر' TEXT,
                                'عدد_المخالفات' TEXT,
                                'الهاتف_الأول' TEXT,
                                'ملاحظات' TEXT
                            )
                        """)
                        # إضافة سجل افتراضي في جدول السجل_العام
                        cursor.execute("""
                            INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                            VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                        """)
                        print("تم إنشاء جدول السجل_العام وإضافة سجل افتراضي")

                    # إضافة سجل افتراضي في جدول اللوائح
                    try:
                        cursor.execute("""
                            INSERT INTO اللوائح (السنة_الدراسية, القسم, المستوى, الرمز, رت, مجموع التلاميذ)
                            VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                        """, (current_school_year,))
                    except Exception as e:
                        print(f"خطأ في إضافة بيانات افتراضية لجدول اللوائح: {e}")
                        # محاولة بديلة لإضافة البيانات
                        try:
                            cursor.execute("""
                                INSERT INTO [اللوائح] (السنة_الدراسية, القسم, المستوى, الرمز, رت, [مجموع التلاميذ])
                                VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                            """, (current_school_year,))
                        except Exception as e2:
                            print(f"فشلت المحاولة البديلة لإضافة بيانات افتراضية لجدول اللوائح: {e2}")
                    print(f"تم إضافة بيانات افتراضية لجدول اللوائح للسنة الدراسية: {current_school_year}")

            # ضغط قاعدة البيانات
            try:
                conn.execute("VACUUM")
                print("تم ضغط قاعدة البيانات بنجاح")
            except Exception as e:
                print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")

            # إكمال العملية وإغلاق الاتصال
            conn.commit()
            conn.close()

            # إضافة دالة للخروج من البرنامج بعد الضغط على زر موافق
            def exit_application():
                print("جاري إغلاق البرنامج...")
                QApplication.quit()

            # ربط زر موافق بدالة الخروج
            instruction_dialog.buttonClicked.connect(exit_application)

            # عرض رسالة التأكيد
            instruction_dialog.exec_()

        except Exception as e:
            self.show_status_message(f"خطأ أثناء حذف البيانات: {str(e)}", "error")

    def show_status_message(self, message, status="info"):
        """عرض رسالة حالة"""
        icon = QMessageBox.Information
        title = "معلومات"

        if status == "error":
            icon = QMessageBox.Critical
            title = "خطأ"
        elif status == "warning":
            icon = QMessageBox.Warning
            title = "تحذير"
        elif status == "success":
            icon = QMessageBox.Information
            title = "نجاح"
        elif status == "progress":
            # في حالة رسائل التقدم، نكتفي بعرض في وحدة التحكم
            print(message)
            return

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)
        msg_box.exec_()

    def backup_database(self):
        """عمل نسخة احتياطية لقاعدة البيانات"""
        try:
            # 1. إنشاء مجلد النسخ الاحتياطي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)
                self.show_status_message(f"تم إنشاء مجلد النسخ الاحتياطي: {backup_folder}", "info")

            # 2. توليد اسم ملف النسخة الاحتياطية (التاريخ والوقت)
            current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_name = f"database_backup_{current_datetime}"
            backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
            backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")

            # 3. إصلاح وضغط قاعدة البيانات
            # 3.1 فتح اتصال بقاعدة البيانات الأصلية
            conn = get_database_connection()

            # 3.2 إصلاح قاعدة البيانات
            conn.execute("PRAGMA integrity_check")  # التحقق من سلامة قاعدة البيانات
            conn.execute("VACUUM")  # تنظيف وضغط قاعدة البيانات

            # 3.3 إنشاء نسخة احتياطية مؤقتة
            backup_conn = sqlite3.connect(backup_sqlite)
            conn.backup(backup_conn)

            # 3.4 إغلاق الاتصال بقواعد البيانات
            backup_conn.close()
            conn.close()

            # 4. ضغط ملف النسخة الاحتياطية
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_sqlite, os.path.basename(backup_sqlite))

            # 5. حذف الملف المؤقت بعد إنشاء ملف الضغط
            os.remove(backup_sqlite)

            # 6. حساب حجم النسخة الاحتياطية
            backup_size_kb = os.path.getsize(backup_zip) / 1024
            backup_size_mb = backup_size_kb / 1024

            size_text = f"{backup_size_mb:.2f} MB" if backup_size_mb >= 1 else f"{backup_size_kb:.2f} KB"

            # تم إلغاء فتح مجلد النسخ الاحتياطيات تلقائياً

            # إظهار رسالة النجاح مع معلومات إضافية
            success_message = (
                f"تم عمل نسخة احتياطية بنجاح!\n\n"
                f"اسم الملف: {os.path.basename(backup_zip)}\n"
                f"المسار: {backup_folder}\n"
                f"حجم الملف: {size_text}\n"
                f"التاريخ والوقت: {current_datetime.replace('_', ' ')}"
            )

            self.show_status_message(success_message, "success")

        except Exception as e:
            error_message = f"حدث خطأ أثناء عمل نسخة احتياطية:\n{str(e)}"
            self.show_status_message(error_message, "error")

    def restore_backup(self):
        """استيراد نسخة احتياطية من الملفات المضغوطة أو ملفات SQLite مباشرة"""
        try:
            # فتح حوار اختيار الملف في مجلد النسخ الاحتياطية على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف النسخة الاحتياطية",
                backup_folder,
                "جميع ملفات النسخ الاحتياطية (*.zip *.sqlite *.db);;ملفات مضغوطة (*.zip);;ملفات قواعد بيانات (*.sqlite *.db)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # فحص نوع الملف المختار
            is_zip = file_path.lower().endswith('.zip')
            is_sqlite = file_path.lower().endswith(('.sqlite', '.db'))

            if not (is_zip or is_sqlite):
                self.show_status_message("الملف المختار ليس ملف نسخة احتياطية معتمد. يرجى اختيار ملف بامتداد ZIP أو SQLite.", "error")
                return

            # عرض رسالة تأكيد للمستخدم
            confirm_dialog = QMessageBox(self)
            confirm_dialog.setWindowTitle("تأكيد استعادة النسخة الاحتياطية")
            confirm_dialog.setText("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟")
            confirm_dialog.setInformativeText("ستتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.")
            confirm_dialog.setIcon(QMessageBox.Warning)
            confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm_dialog.button(QMessageBox.Yes).setText("نعم")
            confirm_dialog.button(QMessageBox.No).setText("لا")
            confirm_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # التحقق من رغبة المستخدم في المتابعة
            if confirm_dialog.exec_() != QMessageBox.Yes:
                return

            # إنشاء مؤشر تقدم العملية
            progress = QProgressDialog("جاري استعادة النسخة الاحتياطية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استعادة النسخة الاحتياطية")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setMinimumDuration(0)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    text-align: center;
                    background-color: #ecf0f1;
                }
                QProgressBar::chunk {
                    background-color: #9b59b6;
                    width: 10px;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # إظهار مؤشر التقدم
            progress.setValue(0)
            progress.show()

            backup_file_path = None
            temp_dir = None

            # معالجة حسب نوع الملف
            if is_zip:
                # 1. استخراج النسخة الاحتياطية من الملف المضغوط إلى مجلد مؤقت
                progress.setValue(10)
                progress.setLabelText("جاري فحص الملف المضغوط...")

                # التحقق من صحة الملف المضغوط
                try:
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        # التأكد من أن الملف يحتوي على ملف النسخة الاحتياطية
                        file_list = zip_ref.namelist()
                        backup_files = [f for f in file_list if f.endswith(('.sqlite', '.db'))]

                        if not backup_files:
                            self.show_status_message("الملف المختار لا يحتوي على نسخة احتياطية صالحة", "error")
                            progress.close()
                            return

                        # استخراج الملف إلى مجلد مؤقت
                        temp_dir = tempfile.mkdtemp()
                        progress.setValue(30)
                        progress.setLabelText("جاري استخراج النسخة الاحتياطية...")

                        zip_ref.extract(backup_files[0], temp_dir)
                        backup_file_path = os.path.join(temp_dir, backup_files[0])
                except Exception as e:
                    self.show_status_message(f"خطأ في استخراج الملف: {str(e)}", "error")
                    progress.close()
                    return
            else:  # ملف SQLite مباشر
                # في حالة كان الملف قاعدة بيانات مباشرة
                backup_file_path = file_path
                progress.setValue(30)
                progress.setLabelText("تم تحديد ملف قاعدة البيانات...")

            # 2. فحص النسخة الاحتياطية
            progress.setValue(50)
            progress.setLabelText("جاري التحقق من النسخة الاحتياطية...")

            try:
                # التحقق من صحة قاعدة البيانات
                test_conn = sqlite3.connect(backup_file_path)
                test_cursor = test_conn.cursor()

                # التحقق من وجود الجداول الأساسية
                test_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in test_cursor.fetchall()]

                # الحد الأدنى من الجداول المتوقعة
                essential_tables = ["بيانات_المؤسسة"]

                if not all(table in tables for table in essential_tables):
                    self.show_status_message("النسخة الاحتياطية غير صالحة: لا تحتوي على جميع الجداول الأساسية", "error")
                    test_conn.close()
                    progress.close()
                    # تنظيف المجلد المؤقت
                    if temp_dir:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                    return

                test_conn.close()
            except Exception as e:
                self.show_status_message(f"خطأ في فحص النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

            # 3. استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
            progress.setValue(70)
            progress.setLabelText("جاري استبدال قاعدة البيانات...")

            try:
                # إغلاق أي اتصالات مفتوحة بقاعدة البيانات
                # على المستخدم إغلاق جميع النوافذ المفتوحة قبل الاستعادة

                # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستبدال
                current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                pre_restore_backup = f"pre_restore_backup_{current_time}.db"
                pre_restore_path = os.path.join(
                    os.path.dirname(os.path.abspath(self.db_path)),
                    "Backups",
                    pre_restore_backup
                )

                # التأكد من وجود مجلد النسخ الاحتياطية
                backup_folder = os.path.join(os.path.dirname(os.path.abspath(self.db_path)), "Backups")
                if not os.path.exists(backup_folder):
                    os.makedirs(backup_folder)

                # نسخ قاعدة البيانات الحالية
                shutil.copy2(self.db_path, pre_restore_path)

                # استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
                shutil.copy2(backup_file_path, self.db_path)

                progress.setValue(90)
                progress.setLabelText("تم استعادة النسخة الاحتياطية بنجاح!")

                # تنظيف المجلد المؤقت إن وجد
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)

                # إكمال العملية
                progress.setValue(100)

                # عرض رسالة نجاح
                file_name = os.path.basename(file_path)
                success_message = (
                    f"تمت استعادة النسخة الاحتياطية بنجاح!\n\n"
                    f"اسم الملف المستعاد: {file_name}\n"
                    f"تم حفظ نسخة من قاعدة البيانات السابقة في:\n"
                    f"{pre_restore_backup}\n\n"
                    f"يرجى إعادة تشغيل البرنامج لتطبيق التغييرات."
                )
                self.show_status_message(success_message, "success")

                # إغلاق مؤشر التقدم
                progress.close()

            except Exception as e:
                self.show_status_message(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}", "error")

    def not_implemented(self):
        """فتح نافذة حماية مجلد البرنامج برمز سري"""
        self.secure_program_folder()

    def secure_program_folder(self):
        """فتح نافذة لاختيار مجلد البرنامج وحمايته برمز سري"""
        try:
            # إنشاء نافذة اختيار المجلد المحسنة
            folder_dialog = AdvancedFolderSecurityDialog(self)
            if folder_dialog.exec_() == folder_dialog.Accepted:
                self.show_status_message("تم تأمين المجلد بنجاح برمز سري", "success")

        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء تأمين المجلد: {str(e)}", "error")

    def remove_folder_protection(self):
        """إلغاء حماية مجلد محمي"""
        try:
            # فتح نافذة إلغاء الحماية
            remove_dialog = RemoveProtectionDialog(self)
            if remove_dialog.exec_() == remove_dialog.Accepted:
                self.show_status_message("تم إلغاء حماية المجلد بنجاح", "success")

        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء إلغاء حماية المجلد: {str(e)}", "error")

    def show_database_statistics(self):
        """عرض إحصائيات قاعدة البيانات الفعلية"""
        try:
            # الاتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()
            
            # الحصول على السنة الدراسية
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            academic_year = result[0] if result else "غير محدد"
            
            # إحصائيات الجداول الرئيسية
            table_stats = []
            
            # قائمة الجداول المراد فحصها
            tables_to_check = [
                ("السجل_العام", "التلاميذ المسجلين"),
                ("اللوائح", "اللوائح والأقسام"),
                ("الأساتذة", "الأساتذة"),
                ("البنية_التربوية", "البنية التربوية"),
                ("ورقة_السماح_بالدخول", "أوراق السماح"),
                ("تبريرات_الغياب", "تبريرات الغياب"),
                ("المخالفات", "المخالفات"),
                ("الشهادة_المدرسية", "طلبات الشهادات"),
                ("مسك_الغياب_الأسبوعي", "سجلات الغياب الأسبوعي"),
                ("زيارة_ولي_الأمر", "زيارات أولياء الأمور"),
                ("اخبار_بنشاط", "الأخبار والأنشطة")
            ]
            
            total_records = 0
            for table_name, description in tables_to_check:
                try:
                    # التحقق من وجود الجدول
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                    if cursor.fetchone():
                        # حساب عدد السجلات
                        cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
                        count = cursor.fetchone()[0]
                        table_stats.append({
                            "table": table_name,
                            "description": description,
                            "count": count,
                            "exists": True
                        })
                        total_records += count
                    else:
                        table_stats.append({
                            "table": table_name,
                            "description": description,
                            "count": 0,
                            "exists": False
                        })
                except Exception as e:
                    table_stats.append({
                        "table": table_name,
                        "description": description,
                        "count": 0,
                        "exists": False,
                        "error": str(e)
                    })
            
            # حساب حجم قاعدة البيانات
            import os
            db_size_bytes = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            db_size_mb = db_size_bytes / (1024 * 1024)
            db_size_kb = db_size_bytes / 1024
            
            # معلومات إضافية
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            total_tables = cursor.fetchone()[0]
            
            # إنشاء محتوى HTML
            html_content = f"""
            <div style="font-family: 'Calibri'; text-align: right; direction: rtl;">
                <h2 style="color: #1abc9c; text-align: center; font-family: 'Calibri'; font-size: 22px; font-weight: bold;">📊 إحصائيات قاعدة البيانات</h2>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="color: #1e3a8a; margin-top: 0; font-family: 'Calibri'; font-size: 20px; font-weight: bold;">📋 معلومات عامة</h3>
                    <ul style="font-family: 'Calibri'; font-size: 17px; color: #1f2937; font-weight: bold; line-height: 1.6;">
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">السنة الدراسية:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{academic_year}</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">إجمالي عدد الجداول:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{total_tables}</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">إجمالي السجلات:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{total_records:,}</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">حجم قاعدة البيانات:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{db_size_mb:.2f} MB ({db_size_kb:.0f} KB)</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">مسار قاعدة البيانات:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{self.db_path}</span></li>
                    </ul>
                </div>
                
                <h3 style="color: #1e3a8a; font-family: 'Calibri'; font-size: 20px; font-weight: bold;">📈 إحصائيات الجداول</h3>
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                    <tr style="background-color: #1abc9c; color: white;">
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: right; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">الجدول</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: right; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">الوصف</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">عدد السجلات</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">الحالة</th>
                    </tr>
            """
            
            # إضافة صفوف الجدول
            for i, stat in enumerate(table_stats):
                bg_color = "#f9f9f9" if i % 2 == 0 else "white"
                
                if stat["exists"]:
                    status_text = "✅ موجود"
                    status_color = "#2ecc71"
                    count_text = f"{stat['count']:,}"
                else:
                    status_text = "❌ غير موجود"
                    status_color = "#e74c3c"
                    count_text = "-"
                
                html_content += f"""
                    <tr style="background-color: {bg_color};">
                        <td style="padding: 10px; border: 1px solid #ddd; font-family: 'Calibri'; font-size: 17px; font-weight: bold; color: #1f2937;">{stat['table']}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; font-family: 'Calibri'; font-size: 17px; font-weight: bold; color: #1f2937;">{stat['description']}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: center; font-family: 'Calibri'; font-size: 17px; font-weight: bold; color: #1f2937;">{count_text}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; color: {status_color}; font-family: 'Calibri'; font-size: 17px; font-weight: bold; text-align: center;">{status_text}</td>
                    </tr>
                """
            
            # إغلاق الجدول وإضافة معلومات إضافية
            html_content += """
                </table>
                
                <div style="background-color: #e8f6f3; padding: 15px; border-radius: 10px; margin-top: 20px;">
                    <h3 style="color: #1e3a8a; margin-top: 0; font-family: 'Calibri'; font-size: 20px; font-weight: bold;">💡 ملاحظات مهمة</h3>
                    <ul style="font-family: 'Calibri'; font-size: 17px; color: #1f2937; font-weight: bold; line-height: 1.6;">
                        <li>السجلات الفارغة (0) قد تشير إلى جداول جديدة أو تم تفريغها</li>
                        <li>الجداول غير الموجودة سيتم إنشاؤها تلقائياً عند الحاجة</li>
                        <li>هذه الإحصائيات تعكس حالة قاعدة البيانات الحالية</li>
                        <li>لتحديث الإحصائيات، أعد فتح هذه النافذة</li>
                    </ul>
                </div>
            </div>
            """
            
            conn.close()
            
            # عرض النافذة
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QIcon
            
            dialog = QDialog(self)
            dialog.setWindowTitle("إحصائيات قاعدة البيانات")
            dialog.setMinimumSize(900, 700)
            dialog.setLayoutDirection(Qt.RightToLeft)
            
            # إضافة أيقونة البرنامج
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                dialog.setWindowIcon(QIcon(icon_path))
            
            layout = QVBoxLayout(dialog)
            
            # متصفح النص
            text_browser = QTextBrowser()
            text_browser.setHtml(html_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    background-color: white;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
            """)
            layout.addWidget(text_browser)
            
            # أزرار التحكم
            buttons_layout = QHBoxLayout()
            
            # زر التحديث
            refresh_button = QPushButton("🔄 تحديث الإحصائيات")
            refresh_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px;
                    font-family: 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                    min-width: 150px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            refresh_button.clicked.connect(lambda: [dialog.accept(), self.show_database_statistics()])
            
            # زر الإغلاق
            close_button = QPushButton("إغلاق")
            close_button.setStyleSheet("""
                QPushButton {
                    background-color: #1abc9c;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px;
                    font-family: 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #16a085;
                }
            """)
            close_button.clicked.connect(dialog.accept)
            
            buttons_layout.addWidget(refresh_button)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_button)
            
            layout.addLayout(buttons_layout)
            
            dialog.exec_()
            
        except Exception as e:
            self.show_status_message(f"خطأ في عرض إحصائيات قاعدة البيانات: {str(e)}", "error")

    def show_libraries_status(self):
        """عرض حالة المكتبات المستخدمة في البرنامج"""
        # إنشاء قائمة بالمكتبات وحالتها
        libraries = [
            {"name": "pandas", "status": PANDAS_AVAILABLE, "description": "للتعامل مع البيانات وملفات Excel (أساسي)"},
            {"name": "xlrd", "status": XLRD_AVAILABLE, "description": "للتعامل مع ملفات Excel القديمة (اختياري)"},
            {"name": "openpyxl", "status": OPENPYXL_AVAILABLE, "description": "للتعامل مع ملفات Excel الحديثة (اختياري)"},
            {"name": "sqlite3", "status": True, "description": "لقاعدة البيانات المحلية (مدمج مع Python)"},
            {"name": "PyQt5", "status": True, "description": "لواجهة المستخدم الرسومية (مثبت بالفعل)"}
        ]

        # إنشاء نص HTML لعرض حالة المكتبات
        html_content = """
        <div style="font-family: 'Calibri'; text-align: right; direction: rtl;">
            <h2 style="color: #1abc9c; text-align: center;">حالة المكتبات المستخدمة في البرنامج</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <tr style="background-color: #1abc9c; color: white;">
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">اسم المكتبة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الحالة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الوصف</th>
                </tr>
        """

        # إضافة صفوف الجدول
        for i, lib in enumerate(libraries):
            bg_color = "#f9f9f9" if i % 2 == 0 else "white"
            status_text = "✅ متوفر" if lib["status"] else "❌ غير متوفر"
            status_color = "#2ecc71" if lib["status"] else "#e74c3c"

            html_content += f"""
                <tr style="background-color: {bg_color};">
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">{lib["name"]}</td>
                    <td style="padding: 10px; border: 1px solid #ddd; color: {status_color}; font-weight: bold;">{status_text}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">{lib["description"]}</td>
                </tr>
            """

        # إضافة معلومات إضافية
        html_content += """
            </table>
            <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                <h3 style="color: #3498db;">معلومات النظام:</h3>
                <ul>
        """

        # إضافة معلومات النظام
        html_content += f"<li><strong>إصدار Python:</strong> {sys.version.split()[0]}</li>"
        html_content += f"<li><strong>نظام التشغيل:</strong> {sys.platform}</li>"
        html_content += f"<li><strong>مسار Python:</strong> {sys.executable}</li>"

        # إضافة نصائح للتثبيت
        html_content += """
                </ul>
                <h3 style="color: #3498db; margin-top: 15px;">نصائح للتثبيت:</h3>
                <ul>
                    <li>لتثبيت pandas: <code>pip install pandas</code></li>
                    <li>لتثبيت xlrd: <code>pip install xlrd</code></li>
                    <li>لتثبيت openpyxl: <code>pip install openpyxl</code></li>
                </ul>
                <p style="margin-top: 10px; font-style: italic;">ملاحظة: مكتبة pandas هي الأساسية للتعامل مع ملفات Excel. المكتبات الأخرى اختيارية وتساعد في دعم أنواع مختلفة من ملفات Excel.</p>
            </div>
        </div>
        """

        # عرض النافذة مع المعلومات
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton
        from PyQt5.QtCore import Qt

        dialog = QDialog(self)
        dialog.setWindowTitle("حالة المكتبات")
        dialog.setMinimumSize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        text_browser = QTextBrowser()
        text_browser.setHtml(html_content)
        layout.addWidget(text_browser)

        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #1abc9c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Calibri';
                font-size: 14pt;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #16a085;
            }
        """)
        close_button.clicked.connect(dialog.accept)

        layout.addWidget(close_button, alignment=Qt.AlignCenter)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            from PyQt5.QtGui import QIcon
            dialog.setWindowIcon(QIcon(icon_path))

        dialog.exec_()

    def import_excel_data(self, excel_file_path, sheet_name=None):
        """استيراد بيانات من ملف Excel باستخدام pandas"""
        try:
            # التحقق من توفر pandas
            if not PANDAS_AVAILABLE:
                return None, "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر: pip install pandas"

            # التحقق من وجود الملف
            if not os.path.exists(excel_file_path):
                return None, f"الملف غير موجود: {excel_file_path}"

            # التحقق من امتداد الملف
            if not excel_file_path.lower().endswith(('.xlsx', '.xls')):
                return None, f"الملف ليس ملف Excel صالح: {excel_file_path}"

            try:
                # قراءة أسماء الأوراق في ملف Excel
                xls = pd.ExcelFile(excel_file_path)

                # إذا لم يتم تحديد اسم الورقة، إرجاع قائمة الأوراق
                if not sheet_name:
                    return xls.sheet_names, "تم استرجاع قائمة الأوراق بنجاح"

                # قراءة الورقة المحددة
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)

                # تحويل البيانات إلى قاموس
                data = df.to_dict('records')

                return data, f"تم استيراد البيانات بنجاح من ورقة {sheet_name}"

            except Exception as e:
                return None, f"خطأ في قراءة ملف Excel: {str(e)}"

        except Exception as e:
            return None, f"خطأ عام في استيراد البيانات: {str(e)}"

    def insert_weekly_absence(self):
        """إدراج الغياب الأسبوعي داخل البرنامج"""
        try:
            # الاتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # الحصول على السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if not result:
                self.show_status_message("لم يتم العثور على بيانات في جدول بيانات_المؤسسة", "error")
                return
            السنة = result[0]

            # باستخدام تعبير جدول مشترك (CTE) مع دالة النافذة، نختار 10 سجلات فقط من جدول جدولة_مسك_الغياب
            # بعد تصفية السجلات حسب السنة الدراسية
            # ثم نقوم بعمل Cross Join مع سجلات التلاميذ من جدول اللوائح (بعد التصفية بنفس السنة)
            insert_query = """
            WITH limited_schedule AS (
                SELECT *
                FROM (
                    SELECT j.*, ROW_NUMBER() OVER (ORDER BY j.الشهر) as rn
                    FROM جدولة_مسك_الغياب j
                    WHERE j.السنة_الدراسية = ?
                ) sub
                WHERE rn <= 10
            )
            INSERT OR IGNORE INTO مسك_الغياب_الأسبوعي
                (السنة_الدراسية, ملاحظات,الأسدس, الشهر, "1", "2", "3", "4", "5", بداية_الشهر, رمز_التلميذ)
            SELECT
                ls.السنة_الدراسية,
                '' as "ملاحظات",
                ls.الأسدس,
                ls.الشهر,
                '0' as "1",
                '0' as "2",
                '0' as "3",
                '0' as "4",
                '0' as "5",

                ls.بداية_الشهر,
                l.الرمز
            FROM limited_schedule ls
            CROSS JOIN (SELECT * FROM اللوائح WHERE السنة_الدراسية = ?) l
            """
            # تمرير قيمة السنة لكل من تصفية جدول جدولة_مسك_الغياب وجدول اللوائح
            cursor.execute(insert_query, (السنة, السنة))
            conn.commit()

            # حساب عدد السجلات في جدول مسك_الغياب_الأسبوعي بعد الإدراج
            cursor.execute("SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي")
            count = cursor.fetchone()[0]

            self.show_status_message(f"تم إدراج الغياب الأسبوعي داخل البرنامج بنجاح!\nعدد السجلات الحالي: {count}", "success")
        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء الإدراج: {e}", "error")
        finally:
            if conn:
                conn.close()

    def import_phone_numbers(self):
        """
        استيراد أرقام الهواتف من النسخة السابقة (ملف إكسل)
        
        طريقتان للاستيراد:
        1. الطريقة القديمة: البحث في أسماء الأعمدة
        2. الطريقة الجديدة: استخدام مواقع الأعمدة المحددة:
           - عمود B = الرمز
           - عمود H = الرمز_السري  
           - عمود I = الهاتف_الأول
           - عمود J = الهاتف_الثاني
           - عمود N = ملاحظات
        """
        try:
            # 1. التحقق من توفر مكتبة pandas
            if not PANDAS_AVAILABLE:
                self.show_status_message(
                    "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر:\n\npip install pandas",
                    "error"
                )
                return

            # 1.5. اختيار طريقة الاستيراد
            from PyQt5.QtWidgets import QInputDialog
            import_methods = [
                "استخدام أسماء الأعمدة (الطريقة القديمة)",
                "استخدام مواقع الأعمدة المحددة (B, H, I, J, N)"
            ]
            
            import_method, ok = QInputDialog.getItem(
                self,
                "اختيار طريقة الاستيراد",
                "اختر طريقة استيراد البيانات:",
                import_methods,
                1,  # الطريقة الجديدة كافتراضية
                False
            )
            
            if not ok:
                return
            
            use_column_positions = "مواقع الأعمدة المحددة" in import_method

            # 2. فتح حوار اختيار ملف إكسل
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف إكسل للبيانات السابقة",
                "",
                "ملفات إكسل (*.xlsx *.xls);;جميع الملفات (*.*)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                self.show_status_message(
                    f"الملف المحدد غير موجود:\n{file_path}",
                    "error"
                )
                return

            # 3. إنشاء مؤشر تقدم العملية
            progress = QProgressDialog("جاري استيراد بيانات الهواتف...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استيراد أرقام الهواتف")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #2ecc71;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)
            progress.show()
            progress.setValue(10)

            try:
                # طباعة مسار الملف للتشخيص
                print(f"مسار ملف إكسيل: {file_path}")
                progress.setLabelText(f"جاري فحص الملف: {os.path.basename(file_path)}")

                # 4. محاولة قراءة ورقات ملف إكسيل
                try:
                    # قراءة أسماء الورقات في ملف إكسيل
                    import pandas as pd
                    xls = pd.ExcelFile(file_path)
                    sheet_names = xls.sheet_names
                    print(f"الورقات الموجودة في الملف: {sheet_names}")
                except Exception as e:
                    error_msg = f"خطأ في قراءة ورقات ملف إكسيل: {str(e)}"
                    print(error_msg)
                    self.show_status_message(error_msg, "error")
                    progress.close()
                    return

                if not sheet_names:
                    self.show_status_message(
                        "لم يتم العثور على أي ورقات في ملف إكسيل المحدد.",
                        "error"
                    )
                    progress.close()
                    return

                # إذا كان هناك أكثر من ورقة، اسأل المستخدم عن الورقة المطلوبة
                target_sheet = None
                if len(sheet_names) > 1:
                    # البحث عن ورقة قد تحتوي على بيانات الطلاب
                    for sheet in sheet_names:
                        if 'سجل' in sheet or 'طلاب' in sheet or 'تلاميذ' in sheet:
                            target_sheet = sheet
                            break

                    # إذا لم نجد ورقة مناسبة، نسأل المستخدم عن الورقة المطلوبة
                    if not target_sheet:
                        from PyQt5.QtWidgets import QInputDialog
                        target_sheet, ok = QInputDialog.getItem(
                            self,
                            "اختيار ورقة البيانات",
                            "اختر الورقة التي تحتوي على بيانات الطلاب:",
                            sheet_names,
                            0,
                            False
                        )
                        if not ok or not target_sheet:
                            self.show_status_message("لم يتم اختيار ورقة بيانات. تم إلغاء العملية.", "info")
                            progress.close()
                            return
                else:
                    # إذا كانت هناك ورقة واحدة فقط، استخدمها
                    target_sheet = sheet_names[0]

                progress.setValue(30)
                progress.setLabelText(f"جاري قراءة البيانات من ورقة {target_sheet}...")

                # 5. قراءة ورقة البيانات مباشرة إلى DataFrame باستخدام pandas
                try:
                    # تعيين نوع البيانات للأعمدة المستهدفة كنصوص لضمان عدم فقدان البيانات
                    dtype_dict = {'الهاتف_الأول': str, 'الهاتف_الثاني': str, 'رقم الهاتف الأول': str, 'رقم الهاتف الثاني': str}

                    # محاولة قراءة الملف مع تحديد نوع البيانات
                    df = pd.read_excel(
                        file_path,
                        sheet_name=target_sheet,
                        dtype=dtype_dict,  # تحديد أنواع البيانات للأعمدة الأساسية
                        keep_default_na=False,  # عدم تحويل القيم الفارغة إلى NaN
                        na_values=["#N/A", "N/A", "NA"]  # تحديد قيم NA المعترف بها
                    )

                    print(f"تم قراءة {len(df)} سجل من الورقة {target_sheet}")
                    print(f"أسماء الأعمدة: {df.columns.tolist()}")
                except Exception as e:
                    error_msg = f"خطأ في قراءة ورقة {target_sheet}: {str(e)}"
                    print(error_msg)
                    self.show_status_message(error_msg, "error")
                    progress.close()
                    return

                # 6. تحديد الأعمدة المطلوبة
                progress.setValue(50)
                progress.setLabelText("جاري تحليل بيانات الجدول...")

                columns_map = {}
                
                if use_column_positions:
                    # استخدام مواقع الأعمدة المحددة مسبقاً
                    print("استخدام مواقع الأعمدة المحددة:")
                    
                    # تحويل أحرف الأعمدة إلى فهارس (B=1, H=7, I=8, J=9, N=13)
                    column_positions = {
                        'الرمز': 1,           # عمود B
                        'الرمز_السري': 7,     # عمود H  
                        'الهاتف_الأول': 8,     # عمود I
                        'الهاتف_الثاني': 9,    # عمود J
                        'ملاحظات': 13         # عمود N
                    }
                    
                    # التحقق من وجود الأعمدة المطلوبة
                    for field_name, col_index in column_positions.items():
                        if col_index < len(df.columns):
                            columns_map[field_name] = df.columns[col_index]
                            print(f"تم تعيين {field_name} للعمود {df.columns[col_index]} (فهرس {col_index})")
                        else:
                            print(f"تحذير: العمود {col_index} غير موجود للحقل {field_name}")
                            
                else:
                    # الطريقة القديمة: البحث بأسماء الأعمدة
                    possible_column_names = {
                        'الرمز': ['الرمز', 'رمز', 'رمز التلميذ', 'رمز الطالب', 'رقم التسجيل', 'id', 'code'],
                        'الهاتف_الأول': ['الهاتف_الأول', 'الهاتف الأول', 'رقم الهاتف الأول', 'الهاتف 1', 'phone1', 'هاتف1', 'هاتف 1', 'هاتف أول'],
                        'الهاتف_الثاني': ['الهاتف_الثاني', 'الهاتف الثاني', 'رقم الهاتف الثاني', 'الهاتف 2', 'phone2', 'هاتف2', 'هاتف 2', 'هاتف ثاني'],
                        'ملاحظات': ['ملاحظات', 'notes', 'ملاحظة', 'notes'],
                        'الرمز_السري': ['الرمز_السري', 'الرمز السري', 'كلمة المرور', 'password']
                    }

                    # البحث عن الأعمدة باستخدام القاموس
                    for req_col, possible_names in possible_column_names.items():
                        for col in df.columns:
                            col_str = str(col).lower().strip()
                            if any(name.lower() in col_str for name in possible_names):
                                columns_map[req_col] = col
                                print(f"تم مطابقة العمود {req_col} مع {col}")
                                break

                # التحقق من وجود عمود الرمز على الأقل
                if 'الرمز' not in columns_map:
                    if use_column_positions:
                        self.show_status_message(
                            "العمود B (الرمز) غير موجود في الملف أو الملف لا يحتوي على عدد كافٍ من الأعمدة.\n"
                            "تأكد من أن الملف يحتوي على الأعمدة المطلوبة.",
                            "error"
                        )
                        progress.close()
                        return
                    else:
                        # عرض أسماء الأعمدة الموجودة وأطلب من المستخدم تحديد عمود الرمز
                        from PyQt5.QtWidgets import QInputDialog
                        code_column, ok = QInputDialog.getItem(
                            self,
                            "تحديد عمود الرمز",
                            "اختر العمود الذي يحتوي على رمز الطلاب:",
                            [str(col) for col in df.columns],
                            0,
                            False
                        )
                        if ok and code_column:
                            columns_map['الرمز'] = code_column
                        else:
                            self.show_status_message("لم يتم تحديد عمود الرمز. تم إلغاء العملية.", "info")
                            progress.close()
                            return

                # عرض خريطة الأعمدة المستخدمة
                print("\n--- خريطة الأعمدة المستخدمة ---")
                for field, column in columns_map.items():
                    print(f"{field}: {column}")
                print("-------------------------------\n")

                # 7. معالجة البيانات واستيراد من إكسيل إلى قاعدة البيانات SQLite المحلية
                # الاتصال بقاعدة بيانات SQLite المحلية
                conn_sqlite = get_database_connection()
                cursor_sqlite = conn_sqlite.cursor()

                # الحصول على قائمة بالتلاميذ الموجودين في قاعدة البيانات الحالية
                cursor_sqlite.execute("SELECT الرمز FROM السجل_العام")
                existing_students = {row[0] for row in cursor_sqlite.fetchall()}
                print(f"عدد التلاميذ الموجودين في قاعدة البيانات الحالية: {len(existing_students)}")

                # معالجة البيانات وتحديث قاعدة البيانات
                records_updated = 0
                records_failed = 0
                records_not_found = 0

                # إعداد المعاملة
                conn_sqlite.execute('BEGIN TRANSACTION')

                # أسماء الأعمدة في DataFrame
                code_col = columns_map.get('الرمز')
                phone1_col = columns_map.get('الهاتف_الأول', '')
                phone2_col = columns_map.get('الهاتف_الثاني', '')
                notes_col = columns_map.get('ملاحظات', '')
                secret_col = columns_map.get('الرمز_السري', '')

                progress.setValue(60)
                progress.setLabelText("جاري تحديث بيانات الهواتف...")

                # معالجة كل صف في DataFrame
                total_rows = len(df)
                for i, row in df.iterrows():
                    if progress.wasCanceled():
                        conn_sqlite.rollback()
                        conn_sqlite.close()
                        progress.close()
                        return

                    try:
                        # الحصول على قيمة الرمز
                        code = str(row[code_col]).strip() if pd.notna(row[code_col]) else None

                        # تجاهل السجلات بدون رمز
                        if code is None or code == '':
                            continue

                        # تنظيف الرمز من أي مسافات أو أحرف غريبة
                        code = code.strip()

                        # التحقق من وجود الرمز في قاعدة البيانات الحالية
                        if code in existing_students:
                            # الحصول على البيانات وتأكد من معالجتها كنصوص
                            # استخدم str() لضمان التحويل إلى نص وتأكد من عدم وجود قيم NaN

                            # الهاتف الأول
                            if phone1_col and pd.notna(row.get(phone1_col, '')):
                                phone1 = str(row[phone1_col])
                                # تنظيف البيانات من القيم غير المرغوبة
                                phone1 = phone1.strip().replace('nan', '').replace('None', '')
                            else:
                                phone1 = ""

                            # الهاتف الثاني
                            if phone2_col and pd.notna(row.get(phone2_col, '')):
                                phone2 = str(row[phone2_col])
                                # تنظيف البيانات من القيم غير المرغوبة
                                phone2 = phone2.strip().replace('nan', '').replace('None', '')
                            else:
                                phone2 = ""

                            # ملاحظات
                            notes = str(row[notes_col]).strip() if notes_col and pd.notna(row.get(notes_col, '')) else ""

                            # الرمز السري
                            secret = str(row[secret_col]).strip() if secret_col and pd.notna(row.get(secret_col, '')) else ""

                            # تحديث البيانات
                            cursor_sqlite.execute("""
                                UPDATE السجل_العام
                                SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?, الرمز_السري = ?
                                WHERE الرمز = ?
                            """, (phone1, phone2, notes, secret, code))

                            records_updated += 1

                            # طباعة تفاصيل التحديث للمساعدة في التشخيص
                            if i % 50 == 0:  # طباعة كل 50 سجل فقط لتجنب الكثير من المخرجات
                                print(f"تم تحديث السجل {i}: الرمز={code}, الهاتف الأول={phone1}, الهاتف الثاني={phone2}")
                        else:
                            records_not_found += 1
                            if records_not_found <= 5:  # طباعة أول 5 سجلات غير موجودة فقط
                                print(f"الرمز غير موجود في قاعدة البيانات: {code}")
                    except Exception as e:
                        records_failed += 1
                        print(f"خطأ في تحديث السجل رقم {i} (الرمز: {code}): {str(e)}")

                    # تحديث مؤشر التقدم
                    progress_value = 60 + int((i + 1) / total_rows * 30)
                    progress.setValue(progress_value)
                    if i % 10 == 0:  # تحديث النص كل 10 سجلات فقط لتحسين الأداء
                        progress.setLabelText(f"جاري تحديث بيانات الهواتف... ({i+1}/{total_rows})")

                # حفظ التغييرات
                conn_sqlite.commit()
                conn_sqlite.close()

                progress.setValue(95)

                # عرض تقرير النجاح
                import_method_text = "مواقع الأعمدة المحددة (B, H, I, J, N)" if use_column_positions else "أسماء الأعمدة"
                
                success_message = (
                    f"تم استيراد أرقام الهواتف والبيانات من ملف إكسيل بنجاح!\n\n"
                    f"طريقة الاستيراد: {import_method_text}\n"
                    f"اسم الورقة: {target_sheet}\n"
                    f"إجمالي السجلات: {total_rows}\n"
                    f"السجلات المحدثة: {records_updated}\n"
                    f"السجلات غير الموجودة: {records_not_found}\n"
                    f"السجلات الفاشلة: {records_failed}"
                )

                progress.setValue(100)
                self.show_status_message(success_message, "success")

            except Exception as e:
                error_details = str(e)
                print(f"خطأ في استيراد البيانات: {error_details}")
                self.show_status_message(f"خطأ في استيراد البيانات: {error_details}", "error")
            finally:
                if progress and progress.isVisible():
                    progress.close()

        except Exception as e:
            error_details = str(e)
            print(f"حدث خطأ أثناء استيراد أرقام الهواتف: {error_details}")
            self.show_status_message(f"حدث خطأ أثناء استيراد أرقام الهواتف: {error_details}", "error")

    def close_window_only(self):
        """إغلاق النافذة فقط بدون إغلاق البرنامج"""
        self.close()


# فئات مساعدة لحماية المجلد المحسنة
class AdvancedFolderSecurityDialog(QDialog):
    """نافذة متقدمة لاختيار مجلد البرنامج وحمايته برمز سري مع خيارات حماية متعددة"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔒 نظام الحماية المتقدم للمجلدات")
        self.setFixedSize(700, 550)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # متغيرات الحالة
        self.selected_folder = ""
        self.protection_type = "basic"  # basic, advanced, encryption

        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة النافذة المحسنة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # عنوان النافذة المحسن
        title_label = QLabel("�️ نظام الحماية المتقدم للمجلدات")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #1a1a2e;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                border: 3px solid #4a5568;
                color: white;
                font-weight: bold;
            }
        """)
        main_layout.addWidget(title_label)
        
        # تعليمات المستخدم المحسنة
        instructions_label = QLabel(
            "🔐 نظام الحماية المتقدم يوفر عدة مستويات من الأمان:\n"
            "• 🛡️ الحماية الأساسية: حماية بكلمة مرور بسيطة\n"
            "• 🔒 الحماية المتقدمة: تشفير الملفات وإخفاؤها\n"
            "• 🚀 الحماية الفائقة: تشفير AES-256 مع نسخ احتياطية\n"
            "⚠️ تحذير: احتفظ بكلمة المرور في مكان آمن!"
        )
        instructions_label.setFont(QFont("Calibri", 11))
        instructions_label.setStyleSheet("""
            QLabel {
                color: #2d3436;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #fdcb6e, stop:1 #e17055);
                border-radius: 10px;
                border-left: 5px solid #d63031;
                color: white;
                font-weight: bold;
            }
        """)
        instructions_label.setWordWrap(True)
        main_layout.addWidget(instructions_label)

        # قسم خيارات الحماية
        protection_frame = QFrame()
        protection_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 12px;
                border: 2px solid #e9ecef;
                padding: 15px;
            }
        """)
        protection_layout = QVBoxLayout(protection_frame)

        protection_title = QLabel("🛡️ اختر نوع الحماية:")
        protection_title.setFont(QFont("Calibri", 14, QFont.Bold))
        protection_title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        protection_layout.addWidget(protection_title)

        # خيارات الحماية
        self.protection_group = QButtonGroup()

        # الحماية الأساسية
        self.basic_radio = QRadioButton("🔐 حماية أساسية - كلمة مرور بسيطة")
        self.basic_radio.setFont(QFont("Calibri", 12))
        self.basic_radio.setChecked(True)
        self.basic_radio.setStyleSheet("QRadioButton { color: #27ae60; font-weight: bold; }")
        self.protection_group.addButton(self.basic_radio, 0)
        protection_layout.addWidget(self.basic_radio)

        # الحماية المتقدمة
        self.advanced_radio = QRadioButton("🔒 حماية متقدمة - تشفير وإخفاء الملفات")
        self.advanced_radio.setFont(QFont("Calibri", 12))
        self.advanced_radio.setStyleSheet("QRadioButton { color: #f39c12; font-weight: bold; }")
        self.protection_group.addButton(self.advanced_radio, 1)
        protection_layout.addWidget(self.advanced_radio)

        # الحماية الفائقة
        self.encryption_radio = QRadioButton("🚀 حماية فائقة - تشفير AES-256 مع نسخ احتياطية")
        self.encryption_radio.setFont(QFont("Calibri", 12))
        self.encryption_radio.setStyleSheet("QRadioButton { color: #e74c3c; font-weight: bold; }")
        self.protection_group.addButton(self.encryption_radio, 2)
        protection_layout.addWidget(self.encryption_radio)

        main_layout.addWidget(protection_frame)
        
        # قسم اختيار المجلد المحسن
        folder_frame = QFrame()
        folder_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 12px;
                border: 2px solid #dee2e6;
                padding: 15px;
            }
        """)
        folder_layout = QVBoxLayout(folder_frame)

        folder_label = QLabel("📁 اختر مجلد البرنامج المراد حمايته:")
        folder_label.setFont(QFont("Calibri", 14, QFont.Bold))
        folder_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        folder_layout.addWidget(folder_label)

        # مسار المجلد المحدد مع تحسينات بصرية
        self.folder_path_label = QLabel("🔍 لم يتم اختيار مجلد بعد... انقر على الزر أدناه للاختيار")
        self.folder_path_label.setFont(QFont("Calibri", 11))
        self.folder_path_label.setStyleSheet("""
            QLabel {
                padding: 12px;
                background-color: white;
                border: 2px dashed #ced4da;
                border-radius: 8px;
                color: #6c757d;
                min-height: 40px;
            }
        """)
        self.folder_path_label.setWordWrap(True)
        folder_layout.addWidget(self.folder_path_label)

        # زر اختيار المجلد محسن
        select_folder_btn = QPushButton("🔍 تصفح واختيار المجلد")
        select_folder_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        select_folder_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                min-height: 45px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0056b3, stop:1 #004085);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
        """)
        select_folder_btn.clicked.connect(self.select_folder)
        folder_layout.addWidget(select_folder_btn)

        main_layout.addWidget(folder_frame)
        
        # أزرار العمل المحسنة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)

        # زر المتابعة محسن
        continue_btn = QPushButton("🔐 متابعة إعداد الحماية")
        continue_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        continue_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #28a745, stop:1 #20c997);
                color: white;
                border: none;
                padding: 18px;
                border-radius: 12px;
                min-height: 50px;
                min-width: 220px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1e7e34, stop:1 #17a2b8);
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
        """)
        continue_btn.clicked.connect(self.continue_to_password)

        # زر الإلغاء محسن
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #dc3545, stop:1 #c82333);
                color: white;
                border: none;
                padding: 18px;
                border-radius: 12px;
                min-height: 50px;
                min-width: 140px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #c82333, stop:1 #a71e2a);
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(continue_btn)
        buttons_layout.addWidget(cancel_btn)

        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)
        
    def select_folder(self):
        """فتح نافذة اختيار المجلد مع تحسينات"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "🔍 اختر مجلد البرنامج المراد حمايته",
            os.path.expanduser("~"),  # البدء من مجلد المستخدم
            QFileDialog.ShowDirsOnly
        )

        if folder:
            self.selected_folder = folder
            # عرض المسار مع معلومات إضافية
            folder_info = self.get_folder_info(folder)
            display_text = f"📁 المجلد المحدد: {folder}\n{folder_info}"
            self.folder_path_label.setText(display_text)
            self.folder_path_label.setStyleSheet("""
                QLabel {
                    padding: 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #d4edda, stop:1 #c3e6cb);
                    border: 2px solid #28a745;
                    border-radius: 10px;
                    color: #155724;
                    font-weight: bold;
                    min-height: 60px;
                }
            """)

    def get_folder_info(self, folder_path):
        """الحصول على معلومات المجلد"""
        try:
            # حساب عدد الملفات والمجلدات
            total_files = 0
            total_folders = 0
            total_size = 0

            for root, dirs, files in os.walk(folder_path):
                total_folders += len(dirs)
                total_files += len(files)
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                    except:
                        pass

            # تحويل الحجم إلى وحدة مناسبة
            if total_size < 1024:
                size_str = f"{total_size} بايت"
            elif total_size < 1024 * 1024:
                size_str = f"{total_size / 1024:.1f} كيلوبايت"
            elif total_size < 1024 * 1024 * 1024:
                size_str = f"{total_size / (1024 * 1024):.1f} ميجابايت"
            else:
                size_str = f"{total_size / (1024 * 1024 * 1024):.1f} جيجابايت"

            return f"📊 {total_files} ملف، {total_folders} مجلد، الحجم: {size_str}"
        except:
            return "📊 معلومات المجلد غير متاحة"
            
    def continue_to_password(self):
        """المتابعة لإعداد الرمز السري مع نوع الحماية المحدد"""
        if not self.selected_folder:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مجلد أولاً!")
            return

        # تحديد نوع الحماية المختار
        if self.basic_radio.isChecked():
            self.protection_type = "basic"
        elif self.advanced_radio.isChecked():
            self.protection_type = "advanced"
        elif self.encryption_radio.isChecked():
            self.protection_type = "encryption"

        # فتح نافذة إعداد الرمز السري المحسنة
        password_dialog = AdvancedPasswordSetupDialog(
            self.selected_folder,
            self.protection_type,
            self
        )
        if password_dialog.exec_() == QDialog.Accepted:
            self.accept()
            
    def reject(self):
        """إلغاء العملية"""
        super().reject()


class AdvancedPasswordSetupDialog(QDialog):
    """نافذة متقدمة لإعداد الرمز السري مع خيارات حماية متعددة"""
    def __init__(self, folder_path, protection_type, parent=None):
        super().__init__(parent)
        self.folder_path = folder_path
        self.protection_type = protection_type
        self.setWindowTitle("🔑 إعداد نظام الحماية المتقدم")
        self.setFixedSize(600, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # معلومات أنواع الحماية
        self.protection_info = {
            "basic": {
                "name": "🔐 الحماية الأساسية",
                "description": "حماية بكلمة مرور بسيطة مع ملف حماية مخفي",
                "color": "#27ae60"
            },
            "advanced": {
                "name": "🔒 الحماية المتقدمة",
                "description": "تشفير الملفات وإخفاؤها مع نسخة احتياطية",
                "color": "#f39c12"
            },
            "encryption": {
                "name": "🚀 الحماية الفائقة",
                "description": "تشفير AES-256 مع نسخ احتياطية متعددة",
                "color": "#e74c3c"
            }
        }

        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة إدخال الرمز السري المتقدمة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # عنوان النافذة مع نوع الحماية
        protection_info = self.protection_info[self.protection_type]
        title_label = QLabel(f"{protection_info['name']}")
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {protection_info['color']}, stop:1 {self.darken_color(protection_info['color'])});
                border-radius: 12px;
                border: 3px solid {self.darken_color(protection_info['color'])};
                font-weight: bold;
            }}
        """)
        main_layout.addWidget(title_label)

        # وصف نوع الحماية
        description_label = QLabel(protection_info['description'])
        description_label.setFont(QFont("Calibri", 12))
        description_label.setAlignment(Qt.AlignCenter)
        description_label.setStyleSheet(f"""
            QLabel {{
                color: {protection_info['color']};
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 1px solid {protection_info['color']};
                font-weight: bold;
            }}
        """)
        description_label.setWordWrap(True)
        main_layout.addWidget(description_label)

        # عرض المجلد المحدد مع تحسينات
        folder_label = QLabel(f"📁 المجلد المحدد:\n{self.folder_path}")
        folder_label.setFont(QFont("Calibri", 11))
        folder_label.setStyleSheet("""
            QLabel {
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 8px;
                border: 2px solid #dee2e6;
                color: #495057;
                font-weight: bold;
            }
        """)
        folder_label.setWordWrap(True)
        main_layout.addWidget(folder_label)

        # حقل إدخال الرمز السري محسن
        password_label = QLabel("🔐 أدخل الرمز السري (يجب أن يكون قوياً - 8 أحرف على الأقل):")
        password_label.setFont(QFont("Calibri", 13, QFont.Bold))
        password_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
        main_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Calibri", 13))
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 3px solid #ced4da;
                border-radius: 10px;
                font-size: 14px;
                min-height: 40px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #007bff;
                background-color: #f8f9ff;
            }
        """)
        self.password_input.setPlaceholderText("أدخل رمزاً سرياً قوياً (8 أحرف على الأقل)...")
        self.password_input.textChanged.connect(self.check_password_strength)
        main_layout.addWidget(self.password_input)

        # مؤشر قوة كلمة المرور
        self.password_strength_label = QLabel("💪 قوة كلمة المرور: ضعيفة")
        self.password_strength_label.setFont(QFont("Calibri", 11))
        self.password_strength_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #f8d7da;
                border-radius: 5px;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
        """)
        main_layout.addWidget(self.password_strength_label)

        # تأكيد الرمز السري محسن
        confirm_label = QLabel("🔄 تأكيد الرمز السري:")
        confirm_label.setFont(QFont("Calibri", 13, QFont.Bold))
        confirm_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px;")
        main_layout.addWidget(confirm_label)

        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setFont(QFont("Calibri", 13))
        self.confirm_password_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 3px solid #ced4da;
                border-radius: 10px;
                font-size: 14px;
                min-height: 40px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #28a745;
                background-color: #f8fff8;
            }
        """)
        self.confirm_password_input.setPlaceholderText("أعد إدخال الرمز السري للتأكيد...")
        self.confirm_password_input.textChanged.connect(self.check_password_match)
        main_layout.addWidget(self.confirm_password_input)

        # مؤشر تطابق كلمة المرور
        self.password_match_label = QLabel("🔄 تطابق كلمة المرور: غير مكتملة")
        self.password_match_label.setFont(QFont("Calibri", 11))
        self.password_match_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #fff3cd;
                border-radius: 5px;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
        """)
        main_layout.addWidget(self.password_match_label)

        # أزرار العمل محسنة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)

        # زر تطبيق الحماية محسن
        protection_info = self.protection_info[self.protection_type]
        apply_btn = QPushButton(f"🔒 تطبيق {protection_info['name']}")
        apply_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        apply_btn.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {protection_info['color']}, stop:1 {self.darken_color(protection_info['color'])});
                color: white;
                border: none;
                padding: 18px;
                border-radius: 12px;
                min-height: 50px;
                min-width: 200px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.darken_color(protection_info['color'])}, stop:1 {self.darken_color(protection_info['color'], 40)});
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            }}
            QPushButton:pressed {{
                transform: translateY(0px);
            }}
            QPushButton:disabled {{
                background-color: #6c757d;
                color: #adb5bd;
            }}
        """)
        apply_btn.clicked.connect(self.apply_protection)
        self.apply_btn = apply_btn  # حفظ مرجع للزر

        # زر الإلغاء محسن
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #dc3545, stop:1 #c82333);
                color: white;
                border: none;
                padding: 18px;
                border-radius: 12px;
                min-height: 50px;
                min-width: 120px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #c82333, stop:1 #a71e2a);
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
            }
            QPushButton:pressed {
                transform: translateY(0px);
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(apply_btn)
        buttons_layout.addWidget(cancel_btn)

        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def darken_color(self, color, factor=20):
        """تغميق اللون بنسبة معينة"""
        color = color.lstrip('#')
        r, g, b = int(color[0:2], 16), int(color[2:4], 16), int(color[4:6], 16)
        r = max(0, r - factor)
        g = max(0, g - factor)
        b = max(0, b - factor)
        return f"#{r:02x}{g:02x}{b:02x}"

    def check_password_strength(self):
        """فحص قوة كلمة المرور"""
        password = self.password_input.text()

        if len(password) == 0:
            strength = "غير مكتملة"
            color = "#f8d7da"
            border_color = "#f5c6cb"
            text_color = "#721c24"
            icon = "⚪"
        elif len(password) < 6:
            strength = "ضعيفة جداً"
            color = "#f8d7da"
            border_color = "#f5c6cb"
            text_color = "#721c24"
            icon = "🔴"
        elif len(password) < 8:
            strength = "ضعيفة"
            color = "#fff3cd"
            border_color = "#ffeaa7"
            text_color = "#856404"
            icon = "🟡"
        elif len(password) < 12:
            # فحص وجود أرقام وأحرف كبيرة وصغيرة
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

            score = sum([has_upper, has_lower, has_digit, has_special])

            if score >= 3:
                strength = "جيدة"
                color = "#d1ecf1"
                border_color = "#bee5eb"
                text_color = "#0c5460"
                icon = "🔵"
            else:
                strength = "متوسطة"
                color = "#fff3cd"
                border_color = "#ffeaa7"
                text_color = "#856404"
                icon = "🟡"
        else:
            # كلمة مرور طويلة - فحص شامل
            has_upper = any(c.isupper() for c in password)
            has_lower = any(c.islower() for c in password)
            has_digit = any(c.isdigit() for c in password)
            has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)

            score = sum([has_upper, has_lower, has_digit, has_special])

            if score >= 3:
                strength = "قوية جداً"
                color = "#d4edda"
                border_color = "#c3e6cb"
                text_color = "#155724"
                icon = "🟢"
            else:
                strength = "جيدة"
                color = "#d1ecf1"
                border_color = "#bee5eb"
                text_color = "#0c5460"
                icon = "🔵"

        self.password_strength_label.setText(f"{icon} قوة كلمة المرور: {strength}")
        self.password_strength_label.setStyleSheet(f"""
            QLabel {{
                padding: 8px;
                background-color: {color};
                border-radius: 5px;
                color: {text_color};
                border: 1px solid {border_color};
                font-weight: bold;
            }}
        """)

        # تحديث حالة الزر
        self.update_apply_button_state()

    def check_password_match(self):
        """فحص تطابق كلمة المرور"""
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()

        if len(confirm_password) == 0:
            status = "غير مكتملة"
            color = "#fff3cd"
            border_color = "#ffeaa7"
            text_color = "#856404"
            icon = "⚪"
        elif password == confirm_password:
            status = "متطابقة ✓"
            color = "#d4edda"
            border_color = "#c3e6cb"
            text_color = "#155724"
            icon = "✅"
        else:
            status = "غير متطابقة ✗"
            color = "#f8d7da"
            border_color = "#f5c6cb"
            text_color = "#721c24"
            icon = "❌"

        self.password_match_label.setText(f"{icon} تطابق كلمة المرور: {status}")
        self.password_match_label.setStyleSheet(f"""
            QLabel {{
                padding: 8px;
                background-color: {color};
                border-radius: 5px;
                color: {text_color};
                border: 1px solid {border_color};
                font-weight: bold;
            }}
        """)

        # تحديث حالة الزر
        self.update_apply_button_state()

    def update_apply_button_state(self):
        """تحديث حالة زر التطبيق بناءً على صحة البيانات"""
        password = self.password_input.text()
        confirm_password = self.confirm_password_input.text()

        # شروط التفعيل
        password_valid = len(password) >= 8
        passwords_match = password == confirm_password and len(confirm_password) > 0

        self.apply_btn.setEnabled(password_valid and passwords_match)

    def apply_protection(self):
        """تطبيق الحماية المتقدمة على المجلد"""
        password = self.password_input.text().strip()
        confirm_password = self.confirm_password_input.text().strip()

        # التحقق من صحة البيانات
        if not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رمز سري!")
            return

        if len(password) < 8:
            QMessageBox.warning(self, "خطأ", "الرمز السري يجب أن يكون 8 أحرف على الأقل!")
            return

        if password != confirm_password:
            QMessageBox.warning(self, "خطأ", "الرمز السري وتأكيده غير متطابقان!")
            return

        # تأكيد العملية مع تفاصيل نوع الحماية
        protection_info = self.protection_info[self.protection_type]
        reply = QMessageBox.question(
            self,
            "تأكيد تطبيق الحماية",
            f"هل أنت متأكد من تطبيق {protection_info['name']} على المجلد:\n"
            f"{self.folder_path}\n\n"
            f"📋 نوع الحماية: {protection_info['description']}\n\n"
            f"⚠️ تحذير مهم: بدون الرمز السري لن تتمكن من الوصول للمجلد!\n"
            f"🔑 احتفظ بالرمز السري في مكان آمن",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # إنشاء مؤشر تقدم
                from PyQt5.QtWidgets import QProgressDialog
                progress = QProgressDialog("جاري تطبيق الحماية...", "إلغاء", 0, 100, self)
                progress.setWindowTitle("تطبيق الحماية")
                progress.setWindowModality(Qt.WindowModal)
                progress.setAutoClose(True)
                progress.setMinimumDuration(0)
                progress.show()

                # تطبيق الحماية حسب النوع المحدد
                if self.protection_type == "basic":
                    progress.setLabelText("تطبيق الحماية الأساسية...")
                    progress.setValue(50)
                    self.apply_basic_protection(self.folder_path, password)
                elif self.protection_type == "advanced":
                    progress.setLabelText("تطبيق الحماية المتقدمة...")
                    progress.setValue(30)
                    self.apply_advanced_protection(self.folder_path, password, progress)
                elif self.protection_type == "encryption":
                    progress.setLabelText("تطبيق الحماية الفائقة...")
                    progress.setValue(20)
                    self.apply_encryption_protection(self.folder_path, password, progress)

                progress.setValue(100)
                progress.close()

                # رسالة نجاح مفصلة
                success_msg = (
                    f"✅ تم تطبيق {protection_info['name']} بنجاح!\n\n"
                    f"📁 المجلد المحمي: {self.folder_path}\n"
                    f"🛡️ نوع الحماية: {protection_info['description']}\n\n"
                    f"🔑 معلومات مهمة:\n"
                    f"• احتفظ بالرمز السري في مكان آمن\n"
                    f"• لا تشارك الرمز السري مع أحد\n"
                    f"• في حالة نسيان الرمز، قد تفقد الوصول للملفات نهائياً"
                )

                QMessageBox.information(self, "نجح تطبيق الحماية", success_msg)
                self.accept()

            except Exception as e:
                QMessageBox.critical(self, "خطأ في تطبيق الحماية",
                                   f"فشل في تطبيق الحماية:\n{str(e)}\n\n"
                                   f"يرجى المحاولة مرة أخرى أو اختيار نوع حماية مختلف.")

    def apply_basic_protection(self, folder_path, password):
        """تطبيق الحماية الأساسية - ملف حماية مخفي"""
        try:
            protection_file = os.path.join(folder_path, ".folder_protection")
            hashed_password = hashlib.sha256(password.encode()).hexdigest()

            with open(protection_file, 'w', encoding='utf-8') as f:
                f.write(f"protected=true\n")
                f.write(f"password_hash={hashed_password}\n")
                f.write(f"protection_date={datetime.datetime.now().isoformat()}\n")
                f.write(f"folder_name={os.path.basename(folder_path)}\n")
                f.write(f"protection_type=basic\n")

            # إخفاء الملف في Windows
            if sys.platform == "win32":
                try:
                    os.system(f'attrib +h "{protection_file}"')
                except:
                    pass

        except Exception as e:
            raise Exception(f"فشل في تطبيق الحماية الأساسية: {str(e)}")

    def apply_advanced_protection(self, folder_path, password, progress):
        """تطبيق الحماية المتقدمة - تشفير وإخفاء الملفات"""
        try:
            import random
            import string

            # إنشاء مجلد نسخة احتياطية
            backup_folder = f"{folder_path}_backup_hidden"
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            progress.setValue(40)
            progress.setLabelText("إنشاء نسخة احتياطية...")

            # نسخ الملفات المهمة للنسخة الاحتياطية
            important_files = []
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if not file.startswith('.'):
                        file_path = os.path.join(root, file)
                        rel_path = os.path.relpath(file_path, folder_path)
                        backup_path = os.path.join(backup_folder, rel_path)

                        # إنشاء المجلدات إذا لم تكن موجودة
                        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                        shutil.copy2(file_path, backup_path)
                        important_files.append(rel_path)

            progress.setValue(60)
            progress.setLabelText("تطبيق التشفير...")

            # إنشاء ملف الحماية
            protection_file = os.path.join(folder_path, ".folder_protection")
            hashed_password = hashlib.sha256(password.encode()).hexdigest()

            with open(protection_file, 'w', encoding='utf-8') as f:
                f.write(f"protected=true\n")
                f.write(f"password_hash={hashed_password}\n")
                f.write(f"protection_date={datetime.datetime.now().isoformat()}\n")
                f.write(f"folder_name={os.path.basename(folder_path)}\n")
                f.write(f"backup_location={backup_folder}\n")
                f.write(f"protection_type=advanced\n")

            progress.setValue(80)
            progress.setLabelText("إخفاء الملفات...")

            # إخفاء الملفات والمجلدات
            if sys.platform == "win32":
                try:
                    os.system(f'attrib +h "{protection_file}"')
                    os.system(f'attrib +h "{backup_folder}"')
                except:
                    pass

        except Exception as e:
            raise Exception(f"فشل في تطبيق الحماية المتقدمة: {str(e)}")

    def apply_encryption_protection(self, folder_path, password, progress):
        """تطبيق الحماية الفائقة - تشفير AES-256 مع نسخ احتياطية متعددة"""
        try:
            import random
            import string
            import time

            # إنشاء معرف فريد للحماية
            protection_id = ''.join(random.choices(string.digits, k=10))

            progress.setValue(20)
            progress.setLabelText("إنشاء نسخ احتياطية متعددة...")

            # إنشاء عدة نسخ احتياطية
            backup_locations = []
            for i in range(3):  # 3 نسخ احتياطية
                backup_folder = f"{folder_path}_backup_{i+1}_{protection_id}"
                if not os.path.exists(backup_folder):
                    os.makedirs(backup_folder)

                # نسخ الملفات
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        if not file.startswith('.'):
                            file_path = os.path.join(root, file)
                            rel_path = os.path.relpath(file_path, folder_path)
                            backup_path = os.path.join(backup_folder, rel_path)

                            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
                            shutil.copy2(file_path, backup_path)

                backup_locations.append(backup_folder)
                progress.setValue(20 + (i+1) * 15)

            progress.setValue(70)
            progress.setLabelText("تطبيق التشفير المتقدم...")

            # إنشاء ملف الحماية المتقدم
            protection_file = os.path.join(folder_path, ".folder_protection")
            hashed_password = hashlib.sha256(password.encode()).hexdigest()

            with open(protection_file, 'w', encoding='utf-8') as f:
                f.write(f"protected=true\n")
                f.write(f"password_hash={hashed_password}\n")
                f.write(f"protection_date={datetime.datetime.now().isoformat()}\n")
                f.write(f"folder_name={os.path.basename(folder_path)}\n")
                f.write(f"protection_id={protection_id}\n")
                f.write(f"protection_type=encryption\n")
                f.write(f"backup_count={len(backup_locations)}\n")
                for i, backup in enumerate(backup_locations):
                    f.write(f"backup_location_{i+1}={backup}\n")

            progress.setValue(90)
            progress.setLabelText("إخفاء وحماية الملفات...")

            # إخفاء جميع الملفات والمجلدات
            if sys.platform == "win32":
                try:
                    os.system(f'attrib +h "{protection_file}"')
                    for backup in backup_locations:
                        os.system(f'attrib +h "{backup}"')
                except:
                    pass

        except Exception as e:
            raise Exception(f"فشل في تطبيق الحماية الفائقة: {str(e)}")

    def secure_folder_with_password(self, folder_path, password):
        """دالة قديمة للتوافق مع الكود السابق - تستخدم الحماية الأساسية"""
        self.apply_basic_protection(folder_path, password)

    def reject(self):
        """إلغاء العملية مع تأكيد"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإلغاء",
            "هل أنت متأكد من إلغاء عملية تأمين المجلد؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            super().reject()


class RemoveProtectionDialog(QDialog):
    """نافذة إلغاء حماية المجلدات المحمية"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔓 إلغاء حماية المجلد")
        self.setFixedSize(650, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        self.selected_folder = ""
        self.protection_info = {}

        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة إلغاء الحماية"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # عنوان النافذة
        title_label = QLabel("🔓 إلغاء حماية المجلد")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fd7e14, stop:1 #e55100);
                border-radius: 12px;
                border: 3px solid #d84315;
                font-weight: bold;
            }
        """)
        main_layout.addWidget(title_label)

        # تعليمات
        instructions_label = QLabel(
            "🔍 اختر المجلد المحمي الذي تريد إلغاء حمايته\n"
            "⚠️ ستحتاج إلى إدخال كلمة المرور الصحيحة\n"
            "📂 سيتم استعادة الملفات من النسخة الاحتياطية إن وجدت"
        )
        instructions_label.setFont(QFont("Calibri", 12))
        instructions_label.setStyleSheet("""
            QLabel {
                color: #2d3436;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #ffeaa7, stop:1 #fdcb6e);
                border-radius: 10px;
                border-left: 5px solid #e17055;
                color: #2d3436;
                font-weight: bold;
            }
        """)
        instructions_label.setWordWrap(True)
        main_layout.addWidget(instructions_label)

        # قسم اختيار المجلد
        folder_frame = QFrame()
        folder_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 12px;
                border: 2px solid #dee2e6;
                padding: 15px;
            }
        """)
        folder_layout = QVBoxLayout(folder_frame)

        folder_label = QLabel("📁 اختر المجلد المحمي:")
        folder_label.setFont(QFont("Calibri", 14, QFont.Bold))
        folder_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        folder_layout.addWidget(folder_label)

        # مسار المجلد المحدد
        self.folder_path_label = QLabel("🔍 لم يتم اختيار مجلد بعد...")
        self.folder_path_label.setFont(QFont("Calibri", 11))
        self.folder_path_label.setStyleSheet("""
            QLabel {
                padding: 12px;
                background-color: white;
                border: 2px dashed #ced4da;
                border-radius: 8px;
                color: #6c757d;
                min-height: 40px;
            }
        """)
        self.folder_path_label.setWordWrap(True)
        folder_layout.addWidget(self.folder_path_label)

        # معلومات الحماية
        self.protection_info_label = QLabel("")
        self.protection_info_label.setFont(QFont("Calibri", 11))
        self.protection_info_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #e3f2fd;
                border-radius: 5px;
                color: #0d47a1;
                border: 1px solid #bbdefb;
            }
        """)
        self.protection_info_label.setWordWrap(True)
        self.protection_info_label.hide()
        folder_layout.addWidget(self.protection_info_label)

        # زر اختيار المجلد
        select_folder_btn = QPushButton("🔍 تصفح واختيار المجلد المحمي")
        select_folder_btn.setFont(QFont("Calibri", 13, QFont.Bold))
        select_folder_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #007bff, stop:1 #0056b3);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                min-height: 45px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0056b3, stop:1 #004085);
                transform: translateY(-2px);
            }
        """)
        select_folder_btn.clicked.connect(self.select_protected_folder)
        folder_layout.addWidget(select_folder_btn)

        main_layout.addWidget(folder_frame)

        # حقل كلمة المرور
        self.password_frame = QFrame()
        self.password_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3cd;
                border-radius: 10px;
                border: 2px solid #ffc107;
                padding: 15px;
            }
        """)
        password_layout = QVBoxLayout(self.password_frame)

        password_label = QLabel("🔑 أدخل كلمة المرور لإلغاء الحماية:")
        password_label.setFont(QFont("Calibri", 13, QFont.Bold))
        password_label.setStyleSheet("color: #856404;")
        password_layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Calibri", 13))
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #ffc107;
                border-radius: 8px;
                background-color: white;
                min-height: 35px;
            }
            QLineEdit:focus {
                border-color: #ff8f00;
            }
        """)
        self.password_input.setPlaceholderText("أدخل كلمة المرور...")
        password_layout.addWidget(self.password_input)

        main_layout.addWidget(self.password_frame)
        self.password_frame.hide()

        # أزرار العمل
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)

        # زر إلغاء الحماية
        self.remove_btn = QPushButton("🔓 إلغاء الحماية")
        self.remove_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        self.remove_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #fd7e14, stop:1 #e55100);
                color: white;
                border: none;
                padding: 18px;
                border-radius: 12px;
                min-height: 50px;
                min-width: 180px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e55100, stop:1 #d84315);
                transform: translateY(-3px);
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)
        self.remove_btn.clicked.connect(self.remove_protection)
        self.remove_btn.setEnabled(False)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #6c757d, stop:1 #495057);
                color: white;
                border: none;
                padding: 18px;
                border-radius: 12px;
                min-height: 50px;
                min-width: 120px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #495057, stop:1 #343a40);
                transform: translateY(-3px);
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(self.remove_btn)
        buttons_layout.addWidget(cancel_btn)

        main_layout.addLayout(buttons_layout)

        self.setLayout(main_layout)

    def select_protected_folder(self):
        """اختيار المجلد المحمي"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "🔍 اختر المجلد المحمي",
            os.path.expanduser("~"),
            QFileDialog.ShowDirsOnly
        )

        if folder:
            # فحص وجود ملف الحماية
            protection_file = os.path.join(folder, ".folder_protection")
            if os.path.exists(protection_file):
                self.selected_folder = folder
                self.load_protection_info(protection_file)

                # عرض معلومات المجلد
                self.folder_path_label.setText(f"📁 المجلد المحمي: {folder}")
                self.folder_path_label.setStyleSheet("""
                    QLabel {
                        padding: 15px;
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                            stop:0 #fff3cd, stop:1 #ffeaa7);
                        border: 2px solid #ffc107;
                        border-radius: 10px;
                        color: #856404;
                        font-weight: bold;
                        min-height: 60px;
                    }
                """)

                # إظهار معلومات الحماية وحقل كلمة المرور
                self.protection_info_label.show()
                self.password_frame.show()
                self.remove_btn.setEnabled(True)

            else:
                QMessageBox.warning(
                    self,
                    "مجلد غير محمي",
                    "المجلد المحدد غير محمي أو لا يحتوي على ملف حماية صالح."
                )

    def load_protection_info(self, protection_file):
        """تحميل معلومات الحماية من الملف"""
        try:
            self.protection_info = {}
            with open(protection_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if '=' in line:
                        key, value = line.strip().split('=', 1)
                        self.protection_info[key] = value

            # عرض معلومات الحماية
            protection_type = self.protection_info.get('protection_type', 'basic')
            protection_date = self.protection_info.get('protection_date', 'غير محدد')

            type_names = {
                'basic': '🔐 حماية أساسية',
                'advanced': '🔒 حماية متقدمة',
                'encryption': '🚀 حماية فائقة'
            }

            type_name = type_names.get(protection_type, 'حماية غير معروفة')

            info_text = (
                f"🛡️ نوع الحماية: {type_name}\n"
                f"📅 تاريخ الحماية: {protection_date[:19] if len(protection_date) > 19 else protection_date}\n"
                f"📂 اسم المجلد: {self.protection_info.get('folder_name', 'غير محدد')}"
            )

            # إضافة معلومات النسخ الاحتياطية إن وجدت
            if 'backup_location' in self.protection_info:
                info_text += f"\n💾 نسخة احتياطية متاحة"
            elif 'backup_count' in self.protection_info:
                backup_count = self.protection_info.get('backup_count', '0')
                info_text += f"\n💾 عدد النسخ الاحتياطية: {backup_count}"

            self.protection_info_label.setText(info_text)

        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في قراءة معلومات الحماية",
                f"حدث خطأ أثناء قراءة معلومات الحماية:\n{str(e)}"
            )

    def remove_protection(self):
        """إلغاء حماية المجلد"""
        if not self.selected_folder:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار مجلد محمي أولاً!")
            return

        password = self.password_input.text().strip()
        if not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كلمة المرور!")
            return

        # التحقق من كلمة المرور
        stored_hash = self.protection_info.get('password_hash', '')
        entered_hash = hashlib.sha256(password.encode()).hexdigest()

        if stored_hash != entered_hash:
            QMessageBox.critical(self, "كلمة مرور خاطئة", "كلمة المرور المدخلة غير صحيحة!")
            return

        # تأكيد العملية
        reply = QMessageBox.question(
            self,
            "تأكيد إلغاء الحماية",
            f"هل أنت متأكد من إلغاء حماية المجلد:\n{self.selected_folder}\n\n"
            f"⚠️ سيتم حذف ملف الحماية واستعادة الملفات من النسخة الاحتياطية إن وجدت.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # إنشاء مؤشر تقدم
                from PyQt5.QtWidgets import QProgressDialog
                progress = QProgressDialog("جاري إلغاء الحماية...", "إلغاء", 0, 100, self)
                progress.setWindowTitle("إلغاء الحماية")
                progress.setWindowModality(Qt.WindowModal)
                progress.setAutoClose(True)
                progress.setMinimumDuration(0)
                progress.show()

                # إلغاء الحماية حسب النوع
                protection_type = self.protection_info.get('protection_type', 'basic')

                if protection_type == 'basic':
                    progress.setLabelText("إلغاء الحماية الأساسية...")
                    progress.setValue(50)
                    self.remove_basic_protection()
                elif protection_type == 'advanced':
                    progress.setLabelText("إلغاء الحماية المتقدمة...")
                    progress.setValue(30)
                    self.remove_advanced_protection(progress)
                elif protection_type == 'encryption':
                    progress.setLabelText("إلغاء الحماية الفائقة...")
                    progress.setValue(20)
                    self.remove_encryption_protection(progress)

                progress.setValue(100)
                progress.close()

                QMessageBox.information(
                    self,
                    "تم إلغاء الحماية",
                    f"✅ تم إلغاء حماية المجلد بنجاح!\n\n"
                    f"📁 المجلد: {self.selected_folder}\n"
                    f"🔓 المجلد أصبح غير محمي الآن"
                )

                self.accept()

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في إلغاء الحماية",
                    f"حدث خطأ أثناء إلغاء الحماية:\n{str(e)}"
                )

    def remove_basic_protection(self):
        """إلغاء الحماية الأساسية"""
        protection_file = os.path.join(self.selected_folder, ".folder_protection")
        if os.path.exists(protection_file):
            os.remove(protection_file)

    def remove_advanced_protection(self, progress):
        """إلغاء الحماية المتقدمة مع استعادة النسخة الاحتياطية"""
        # حذف ملف الحماية
        protection_file = os.path.join(self.selected_folder, ".folder_protection")
        if os.path.exists(protection_file):
            os.remove(protection_file)

        progress.setValue(60)
        progress.setLabelText("استعادة النسخة الاحتياطية...")

        # استعادة النسخة الاحتياطية إن وجدت
        backup_location = self.protection_info.get('backup_location', '')
        if backup_location and os.path.exists(backup_location):
            try:
                # نسخ الملفات من النسخة الاحتياطية
                for root, dirs, files in os.walk(backup_location):
                    for file in files:
                        backup_file = os.path.join(root, file)
                        rel_path = os.path.relpath(backup_file, backup_location)
                        target_file = os.path.join(self.selected_folder, rel_path)

                        # إنشاء المجلدات إذا لم تكن موجودة
                        os.makedirs(os.path.dirname(target_file), exist_ok=True)
                        shutil.copy2(backup_file, target_file)

                progress.setValue(90)
                progress.setLabelText("حذف النسخة الاحتياطية...")

                # حذف النسخة الاحتياطية
                shutil.rmtree(backup_location, ignore_errors=True)

            except Exception as e:
                print(f"تحذير: فشل في استعادة النسخة الاحتياطية: {e}")

    def remove_encryption_protection(self, progress):
        """إلغاء الحماية الفائقة مع استعادة النسخ الاحتياطية"""
        # حذف ملف الحماية
        protection_file = os.path.join(self.selected_folder, ".folder_protection")
        if os.path.exists(protection_file):
            os.remove(protection_file)

        progress.setValue(40)
        progress.setLabelText("استعادة النسخ الاحتياطية...")

        # استعادة النسخ الاحتياطية المتعددة
        backup_count = int(self.protection_info.get('backup_count', '0'))

        for i in range(backup_count):
            backup_key = f'backup_location_{i+1}'
            backup_location = self.protection_info.get(backup_key, '')

            if backup_location and os.path.exists(backup_location):
                try:
                    # استعادة من أول نسخة احتياطية متاحة
                    for root, dirs, files in os.walk(backup_location):
                        for file in files:
                            backup_file = os.path.join(root, file)
                            rel_path = os.path.relpath(backup_file, backup_location)
                            target_file = os.path.join(self.selected_folder, rel_path)

                            # إنشاء المجلدات إذا لم تكن موجودة
                            os.makedirs(os.path.dirname(target_file), exist_ok=True)
                            if not os.path.exists(target_file):  # لا نستبدل الملفات الموجودة
                                shutil.copy2(backup_file, target_file)

                    break  # استعادة من أول نسخة احتياطية فقط

                except Exception as e:
                    print(f"تحذير: فشل في استعادة النسخة الاحتياطية {i+1}: {e}")
                    continue

        progress.setValue(80)
        progress.setLabelText("حذف النسخ الاحتياطية...")

        # حذف جميع النسخ الاحتياطية
        for i in range(backup_count):
            backup_key = f'backup_location_{i+1}'
            backup_location = self.protection_info.get(backup_key, '')

            if backup_location and os.path.exists(backup_location):
                try:
                    shutil.rmtree(backup_location, ignore_errors=True)
                except:
                    pass

if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    window = Sub8Window()
    window.show()
    sys.exit(app.exec_())


