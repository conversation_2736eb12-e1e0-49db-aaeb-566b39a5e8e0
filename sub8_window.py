import sqlite3
from database_config import get_database_path, get_database_connection
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QMessageBox, QInputDialog, QFrame, QLabel,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy,
                            QLineEdit, QGridLayout, QFileDialog, QProgressDialog,
                            QApplication, QDialog)
from PyQt5.QtGui import QFont, QColor, QIcon
from PyQt5.QtCore import Qt, QDateTime
import os
import shutil
import zipfile
import datetime
import tempfile
import sys
import hashlib
import time
import stat
import subprocess

# تم إزالة استيراد مكتبات Access لأنها لم تعد مطلوبة
# البرنامج يستخدم الآن ملفات Excel بدلاً من Access

# تعريف متغيرات للتوافق مع الكود القديم
PYODBC_AVAILABLE = False
PANDAS_ACCESS_AVAILABLE = False
print("تم تعطيل دعم ملفات Access. البرنامج يستخدم الآن ملفات Excel فقط.")

# استيراد pandas كبديل للتعامل مع ملفات Excel
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

# استيراد xlrd و openpyxl للتعامل المباشر مع ملفات Excel
try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

class Sub8Window(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(" إعدادات البرنامج ")
        
        # إعداد النافذة كمشروطة مع زر الإغلاق الأساسي فقط
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint | Qt.WindowTitleHint)
        self.setWindowModality(Qt.ApplicationModal)  # جعل النافذة مشروطة
        
        # تعيين الحجم الثابت
        self.setFixedSize(900, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setStyleSheet("""
            background-color: #f5f5f5;
            /* إخفاء أشرطة التمرير العمودية */
            QScrollBar:vertical {
                width: 0px;
                background: transparent;
            }
            QScrollArea {
                border: none;
            }
            QScrollArea > QWidget > QWidget {
                background: transparent;
            }
        """)  # لون خلفية خفيف وإخفاء أشرطة التمرير

        # تعريف مسار قاعدة البيانات باستخدام النظام الجديد
        self.db_path = get_database_path()

        # إنشاء التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)        # إنشاء عنوان الصفحة بتصميم أكبر وأكثر وضوحًا
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background-color: #0D47A1;  /* لون أزرق غامق */
                border-radius: 12px;
                min-height: 80px;  /* زيادة ارتفاع الإطار */
            }
        """)
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)  # زيادة الهوامش

        # إضافة أيقونة للعنوان بحجم أكبر
        title_label = QLabel("⚙️   إعدادات البرنامج ")
        title_label.setFont(QFont("Amiri", 22, QFont.Bold))  # زيادة حجم الخط
        title_label.setStyleSheet("color: white;")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # إضافة تأثير الظل للعنوان
        self.apply_shadow(title_frame)

        # إضافة العنوان للتخطيط الرئيسي
        main_layout.addWidget(title_frame)

        # إنشاء إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
            }
        """)

        # إضافة تأثير الظل للإطار
        self.apply_shadow(buttons_frame)

        # تخطيط الأزرار
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(20, 20, 20, 20)
        buttons_layout.setSpacing(15)  # تقليل المسافة بين الأزرار

        # تعديل التخطيط ليكون شبكي بدلاً من عمودي
        grid_layout = QGridLayout()
        grid_layout.setHorizontalSpacing(15)
        grid_layout.setVerticalSpacing(15)
        buttons_layout.addLayout(grid_layout)

        # إنشاء الأزرار مع إضافة الزر الجديد
        button_data = [
            ("🗑️ حذف جميع البيانات", "#e74c3c", self.delete_all_data),  # أحمر
            ("🔄 تهيئة البرنامج لبداية سنة دراسية جديدة", "#ff9800", self.reset_school_year),  # برتقالي
            ("� تأمين مجلد البرنامج برمز سري", "#3498db", self.not_implemented),  # أزرق - تم تحديث النص والوظيفة
            ("🔒 استيراد أرقام الهواتف من النسخة السابقة", "#2ecc71", self.import_phone_numbers),  # أخضر
            ("💾 نسخ احتياطي للبيانات", "#f39c12", self.backup_database),  # برتقالي فاتح - تم تغيير الدالة
            ("📂 استيراد نسخة احتياطية", "#9b59b6", self.restore_backup),  # بنفسجي - تم تحديث الدالة
            ("📊 إحصائيات قاعدة البيانات", "#1abc9c", self.show_database_statistics),  # فيروزي - إحصائيات قاعدة البيانات الفعلية
            ("📝 إدراج الغياب الأسبوعي", "#27ae60", self.insert_weekly_absence)  # أخضر غامق
        ]        # تنظيم الأزرار في صفين (2 عمود و 4 صف)
        self.buttons = []
        for i, (text, color, handler) in enumerate(button_data):
            btn = QPushButton(text)
            btn.setFont(QFont("Calibri", 15, QFont.Bold))  # توحيد الخط: Calibri 15 أبيض غامق
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    padding: 12px;
                    text-align: right;
                    min-height: 40px;
                    min-width: 200px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, factor=30)};
                }}
            """)
            btn.clicked.connect(handler)

            # ترتيب الأزرار في 2 عمود
            row = i // 2
            col = i % 2
            grid_layout.addWidget(btn, row, col)
            self.buttons.append(btn)

        # إضافة مساحة مرنة في نهاية تخطيط الأزرار
        buttons_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # إضافة إطار الأزرار للتخطيط الرئيسي
        main_layout.addWidget(buttons_frame)

    def apply_shadow(self, widget):
        """تطبيق تأثير الظل على العنصر"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setXOffset(3)
        shadow.setYOffset(3)
        shadow.setColor(QColor(0, 0, 0, 60))
        widget.setGraphicsEffect(shadow)

    def darken_color(self, color, factor=15):
        """تغميق اللون بنسبة معينة"""
        # تحويل اللون من تنسيق hex إلى RGB
        color = color.lstrip('#')
        r, g, b = int(color[0:2], 16), int(color[2:4], 16), int(color[4:6], 16)

        # تقليل قيم RGB بنسبة factor
        r = max(0, r - factor)
        g = max(0, g - factor)
        b = max(0, b - factor)        # إعادة تحويل اللون إلى تنسيق hex
        return f"#{r:02x}{g:02x}{b:02x}"

    def closeEvent(self, event):
        """إغلاق النافذة فقط بدون إنهاء البرنامج"""
        # السماح بإغلاق النافذة مباشرة
        event.accept()

    def delete_all_data(self):
        """حذف جميع البيانات مع التأكد من كلمة المرور"""
        # إنشاء مربع حوار لإدخال كلمة المرور
        password_dialog = QInputDialog(self)
        password_dialog.setWindowTitle("التحقق من الهوية")
        password_dialog.setLabelText("الرجاء إدخال رمز الحذف للمتابعة:")
        password_dialog.setTextEchoMode(QLineEdit.Password)
        password_dialog.setStyleSheet("""
            QInputDialog {
                background-color: white;
            }            QLabel {
                font-family: 'Calibri';
                font-size: 15pt;
                font-weight: bold;
                color: black;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-family: 'Calibri';
                font-size: 15pt;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 15pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)

        ok = password_dialog.exec_()
        password = password_dialog.textValue()

        # التحقق من صحة كلمة المرور
        if ok and password == "12345":
            # تنفيذ عملية الحذف فورا
            self.perform_deletion()
        else:
            if ok:  # إذا ضغط المستخدم على "موافق" لكن كلمة المرور خاطئة
                self.show_status_message("رمز الحذف غير صحيح!", "error")

    def reset_school_year(self):
        """تهيئة البرنامج لبداية سنة دراسية جديدة من خلال حذف بيانات محددة"""
        # إنشاء مربع حوار للتأكيد
        confirm_dialog = QMessageBox(self)
        confirm_dialog.setWindowTitle("تأكيد التهيئة")
        confirm_dialog.setText("هل أنت متأكد من تهيئة البرنامج لسنة دراسية جديدة؟")
        confirm_dialog.setInformativeText("")
        confirm_dialog.setIcon(QMessageBox.Warning)
        confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        confirm_dialog.button(QMessageBox.Yes).setText("نعم")
        confirm_dialog.button(QMessageBox.No).setText("لا")
        confirm_dialog.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)

        # تنفيذ التهيئة إذا تم النقر على نعم
        if confirm_dialog.exec_() == QMessageBox.Yes:
            try:
                # إنشاء مربع حوار لإدخال كلمة المرور للتأكيد
                password_dialog = QInputDialog(self)
                password_dialog.setWindowTitle("التحقق من الهوية")
                password_dialog.setLabelText("الرجاء إدخال رمز التأكيد للمتابعة:")
                password_dialog.setTextEchoMode(QLineEdit.Password)
                password_dialog.setStyleSheet("""
                    QInputDialog {
                        background-color: white;
                    }
                    QLabel {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        color: black;
                    }
                    QLineEdit {
                        padding: 8px;
                        border: 1px solid #bdc3c7;
                        border-radius: 5px;
                        font-family: 'Calibri';
                        font-size: 13pt;
                    }
                    QPushButton {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        min-width: 80px;
                        padding: 5px;
                    }
                """)

                ok = password_dialog.exec_()
                password = password_dialog.textValue()

                # التحقق من صحة كلمة المرور (نفس الرمز المستخدم لحذف جميع البيانات)
                if ok and password == "12345":
                    # تنفيذ عملية التهيئة
                    self.perform_reset()
                else:
                    if ok:  # إذا ضغط المستخدم على "موافق" لكن كلمة المرور خاطئة
                        self.show_status_message("رمز التأكيد غير صحيح!", "error")
            except Exception as e:
                self.show_status_message(f"خطأ أثناء التهيئة: {str(e)}", "error")

    def perform_reset(self):
        """تنفيذ عملية تهيئة البرنامج لبداية سنة دراسية جديدة"""
        try:
            # إنشاء اتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # قائمة الجداول المراد تفريغها
            tables_to_clear = ['ورقة_السماح_بالدخول', 'تبريرات_الغياب', 'المخالفات', 'الشهادة_المدرسية', 'مسك_الغياب_الأسبوعي', 'مسك_أوراق_الفروض', 'زيارة_ولي_الأمر', 'جدول_عام', 'البنية_التربوية']
            tables_cleared = []

            # التحقق من وجود كل جدول قبل محاولة حذف البيانات منه
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM '{table}'")
                    tables_cleared.append(table)

            # إكمال العملية
            conn.commit()
            conn.close()

            # عرض رسالة تأكيد بالجداول التي تم تفريغها
            result_message = "تم تهيئة البرنامج لسنة دراسية جديدة بنجاح!"
            details = "تم حذف البيانات من الجداول التالية:\n"
            for table in tables_cleared:
                details += f"✓ {table}\n"

            self.show_status_message(f"{result_message}\n\n{details}", "success")

        except Exception as e:
            self.show_status_message(f"خطأ أثناء تهيئة البرنامج: {str(e)}", "error")

    def perform_deletion(self):
        """تنفيذ عملية حذف البيانات من الجداول المحددة"""
        try:
            # قائمة الجداول المراد حذف بياناتها
            tables_to_clear = [
                'البنية_التربوية',
                'جدول_عام',
                'زيارة_ولي_الأمر',
                'تبريرات_الغياب',
                'المخالفات',
                'الأساتذة',
                'السجل_العام',
                'اللوائح',
                'الشهادة_المدرسية',
                'ورقة_السماح_بالدخول',
                'اخبار_بنشاط',
                'الرمز_السري',
                'السجل_الاولي',
                'زيارات_أولياء_الأمور',
                'غياب_الأسدس_الأول',
                'غياب_الأسدس_الثاني',
                'مجموع_الغياب_السنوي',
                'مسك_أوراق_الفروض',
                'مسك_الغياب_الأسبوعي'
            ]

            # عرض رسالة تعليمات عادية لتأكيد الحذف
            instruction_dialog = QMessageBox(self)
            instruction_dialog.setWindowTitle("تأكيد الحذف")
            instruction_dialog.setText("تم حذف جميع البيانات بنجاح")

            # إنشاء نص تفصيلي للجداول التي تم حذفها
            deleted_tables_text = "تم مسح البيانات من الجداول التالية:\n"
            for table in tables_to_clear:
                deleted_tables_text += f"• {table}\n"
            deleted_tables_text += "\nمع الاحتفاظ بالسنة الدراسية في جدول بيانات_المؤسسة"
            deleted_tables_text += "\n\nسيتم إغلاق البرنامج الآن لإتمام عملية الحذف وضغط قاعدة البيانات."

            instruction_dialog.setInformativeText(deleted_tables_text)
            instruction_dialog.setIcon(QMessageBox.Information)
            instruction_dialog.setStandardButtons(QMessageBox.Ok)

            # إضافة أيقونة البرنامج إلى نافذة الرسالة
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                instruction_dialog.setWindowIcon(QIcon(icon_path))

            instruction_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: white;
                    background-color: #0D47A1;
                    border: none;
                    border-radius: 5px;
                    padding: 5px 15px;
                    min-width: 140px;
                }
                QPushButton:hover {
                    background-color: #1565C0;
                }
                QPushButton:pressed {
                    background-color: #0D47A1;
                }
            """)

            # تنفيذ حذف البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # التحقق من وجود كل جدول قبل محاولة حذف البيانات منه
            for table in tables_to_clear:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"DELETE FROM {table}")

            # معالجة خاصة لجدول بيانات_المؤسسة - حذف جميع البيانات مع الاحتفاظ بالسنة_الدراسية
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
            if cursor.fetchone():
                # الحصول على السنة الدراسية الحالية
                cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                result = cursor.fetchone()
                current_school_year = result[0] if result else None

                if current_school_year:
                    # الحصول على أسماء جميع الأعمدة في جدول بيانات_المؤسسة
                    cursor.execute("PRAGMA table_info(بيانات_المؤسسة)")
                    columns = [column[1] for column in cursor.fetchall()]

                    # حذف جميع البيانات من الجدول
                    cursor.execute("DELETE FROM بيانات_المؤسسة")

                    # إنشاء استعلام INSERT مع تعيين جميع الأعمدة إلى سلاسل فارغة باستثناء السنة_الدراسية
                    column_names = ", ".join(columns)
                    placeholders = []
                    values = []

                    for column in columns:
                        if column == "السنة_الدراسية":
                            placeholders.append("?")
                            values.append(current_school_year)
                        else:
                            placeholders.append("?")
                            values.append("")  # سلسلة فارغة بدلاً من NULL

                    placeholders_str = ", ".join(placeholders)

                    # إعادة إدخال سجل جديد مع تعيين جميع الأعمدة إلى سلاسل فارغة باستثناء السنة_الدراسية
                    insert_query = f"INSERT INTO بيانات_المؤسسة ({column_names}) VALUES ({placeholders_str})"
                    cursor.execute(insert_query, values)

                    print(f"تم الاحتفاظ بالسنة الدراسية: {current_school_year} وتعيين باقي الحقول إلى سلاسل فارغة")
                else:
                    # إذا لم تكن هناك سنة دراسية، قم بحذف جميع البيانات
                    cursor.execute("DELETE FROM بيانات_المؤسسة")

            # التحقق من وجود جدول اللوائح وإنشائه إذا لم يكن موجوداً
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='اللوائح'")
            if not cursor.fetchone():
                # إنشاء جدول اللوائح إذا لم يكن موجوداً
                try:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS اللوائح (
                            السنة_الدراسية TEXT,
                            القسم TEXT,
                            المستوى TEXT,
                            الرمز TEXT,
                            رت TEXT,
                            مجموع التلاميذ INTEGER DEFAULT 0,
                            PRIMARY KEY(السنة_الدراسية, الرمز)
                        )
                    """)
                    print("تم إنشاء جدول اللوائح")
                except Exception as e:
                    print(f"خطأ في إنشاء جدول اللوائح: {e}")
                    try:
                        cursor.execute("""
                            CREATE TABLE IF NOT EXISTS [اللوائح] (
                                [السنة_الدراسية] TEXT,
                                [القسم] TEXT,
                                [المستوى] TEXT,
                                [الرمز] TEXT,
                                [رت] TEXT,
                                [مجموع التلاميذ] INTEGER DEFAULT 0,
                                PRIMARY KEY([السنة_الدراسية], [الرمز])
                            )
                        """)
                        print("تم إنشاء جدول اللوائح باستخدام الطريقة البديلة")
                    except Exception as e2:
                        print(f"فشل إنشاء جدول اللوائح: {e2}")

            # حذف وإضافة بيانات افتراضية لجدول اللوائح
            try:
                # حذف جميع البيانات من جدول اللوائح
                cursor.execute("DELETE FROM اللوائح")
            except Exception as e:
                print(f"خطأ في حذف بيانات جدول اللوائح: {e}")
                # محاولة بديلة لحذف البيانات
                try:
                    cursor.execute("DELETE FROM [اللوائح]")
                except Exception as e2:
                    print(f"فشلت المحاولة البديلة لحذف بيانات جدول اللوائح: {e2}")

            # إضافة سجل افتراضي لجدول اللوائح
            if current_school_year:
                    # التحقق من وجود جدول السجل_العام
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='السجل_العام'")
                    if cursor.fetchone():
                        # التحقق من وجود السجل في جدول السجل_العام
                        cursor.execute("SELECT COUNT(*) FROM السجل_العام WHERE الرمز = 'A12345678'")
                        if cursor.fetchone()[0] == 0:
                            # إضافة سجل افتراضي في جدول السجل_العام إذا لم يكن موجوداً
                            cursor.execute("""
                                INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                                VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                            """)
                            print("تم إضافة سجل افتراضي في جدول السجل_العام")
                    else:
                        # إنشاء جدول السجل_العام إذا لم يكن موجوداً
                        cursor.execute("""
                            CREATE TABLE IF NOT EXISTS 'السجل_العام' (
                                'الرمز' TEXT PRIMARY KEY,
                                'الاسم_والنسب' TEXT,
                                'السماح' TEXT,
                                'التأخر' TEXT,
                                'عدد_المخالفات' TEXT,
                                'الهاتف_الأول' TEXT,
                                'ملاحظات' TEXT
                            )
                        """)
                        # إضافة سجل افتراضي في جدول السجل_العام
                        cursor.execute("""
                            INSERT INTO السجل_العام (الرمز, الاسم_والنسب, السماح, التأخر, عدد_المخالفات, الهاتف_الأول, ملاحظات)
                            VALUES ('A12345678', 'تلميذ افتراضي', '0', '0', '0', '', '')
                        """)
                        print("تم إنشاء جدول السجل_العام وإضافة سجل افتراضي")

                    # إضافة سجل افتراضي في جدول اللوائح
                    try:
                        cursor.execute("""
                            INSERT INTO اللوائح (السنة_الدراسية, القسم, المستوى, الرمز, رت, مجموع التلاميذ)
                            VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                        """, (current_school_year,))
                    except Exception as e:
                        print(f"خطأ في إضافة بيانات افتراضية لجدول اللوائح: {e}")
                        # محاولة بديلة لإضافة البيانات
                        try:
                            cursor.execute("""
                                INSERT INTO [اللوائح] (السنة_الدراسية, القسم, المستوى, الرمز, رت, [مجموع التلاميذ])
                                VALUES (?, '1APIC-1', 'الأولى إعدادي مسار دولي', 'A12345678', '1', 43)
                            """, (current_school_year,))
                        except Exception as e2:
                            print(f"فشلت المحاولة البديلة لإضافة بيانات افتراضية لجدول اللوائح: {e2}")
                    print(f"تم إضافة بيانات افتراضية لجدول اللوائح للسنة الدراسية: {current_school_year}")

            # ضغط قاعدة البيانات
            try:
                conn.execute("VACUUM")
                print("تم ضغط قاعدة البيانات بنجاح")
            except Exception as e:
                print(f"خطأ أثناء ضغط قاعدة البيانات: {str(e)}")

            # إكمال العملية وإغلاق الاتصال
            conn.commit()
            conn.close()

            # إضافة دالة للخروج من البرنامج بعد الضغط على زر موافق
            def exit_application():
                print("جاري إغلاق البرنامج...")
                QApplication.quit()

            # ربط زر موافق بدالة الخروج
            instruction_dialog.buttonClicked.connect(exit_application)

            # عرض رسالة التأكيد
            instruction_dialog.exec_()

        except Exception as e:
            self.show_status_message(f"خطأ أثناء حذف البيانات: {str(e)}", "error")

    def show_status_message(self, message, status="info"):
        """عرض رسالة حالة"""
        icon = QMessageBox.Information
        title = "معلومات"

        if status == "error":
            icon = QMessageBox.Critical
            title = "خطأ"
        elif status == "warning":
            icon = QMessageBox.Warning
            title = "تحذير"
        elif status == "success":
            icon = QMessageBox.Information
            title = "نجاح"
        elif status == "progress":
            # في حالة رسائل التقدم، نكتفي بعرض في وحدة التحكم
            print(message)
            return

        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
            }
            QLabel {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                color: black;
            }
            QPushButton {
                font-family: 'Calibri';
                font-size: 13pt;
                font-weight: bold;
                min-width: 80px;
                padding: 5px;
            }
        """)
        msg_box.exec_()

    def backup_database(self):
        """عمل نسخة احتياطية لقاعدة البيانات"""
        try:
            # 1. إنشاء مجلد النسخ الاحتياطي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)
                self.show_status_message(f"تم إنشاء مجلد النسخ الاحتياطي: {backup_folder}", "info")

            # 2. توليد اسم ملف النسخة الاحتياطية (التاريخ والوقت)
            current_datetime = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_name = f"database_backup_{current_datetime}"
            backup_sqlite = os.path.join(backup_folder, f"{backup_name}.sqlite")
            backup_zip = os.path.join(backup_folder, f"{backup_name}.zip")

            # 3. إصلاح وضغط قاعدة البيانات
            # 3.1 فتح اتصال بقاعدة البيانات الأصلية
            conn = get_database_connection()

            # 3.2 إصلاح قاعدة البيانات
            conn.execute("PRAGMA integrity_check")  # التحقق من سلامة قاعدة البيانات
            conn.execute("VACUUM")  # تنظيف وضغط قاعدة البيانات

            # 3.3 إنشاء نسخة احتياطية مؤقتة
            backup_conn = sqlite3.connect(backup_sqlite)
            conn.backup(backup_conn)

            # 3.4 إغلاق الاتصال بقواعد البيانات
            backup_conn.close()
            conn.close()

            # 4. ضغط ملف النسخة الاحتياطية
            with zipfile.ZipFile(backup_zip, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(backup_sqlite, os.path.basename(backup_sqlite))

            # 5. حذف الملف المؤقت بعد إنشاء ملف الضغط
            os.remove(backup_sqlite)

            # 6. حساب حجم النسخة الاحتياطية
            backup_size_kb = os.path.getsize(backup_zip) / 1024
            backup_size_mb = backup_size_kb / 1024

            size_text = f"{backup_size_mb:.2f} MB" if backup_size_mb >= 1 else f"{backup_size_kb:.2f} KB"

            # تم إلغاء فتح مجلد النسخ الاحتياطيات تلقائياً

            # إظهار رسالة النجاح مع معلومات إضافية
            success_message = (
                f"تم عمل نسخة احتياطية بنجاح!\n\n"
                f"اسم الملف: {os.path.basename(backup_zip)}\n"
                f"المسار: {backup_folder}\n"
                f"حجم الملف: {size_text}\n"
                f"التاريخ والوقت: {current_datetime.replace('_', ' ')}"
            )

            self.show_status_message(success_message, "success")

        except Exception as e:
            error_message = f"حدث خطأ أثناء عمل نسخة احتياطية:\n{str(e)}"
            self.show_status_message(error_message, "error")

    def restore_backup(self):
        """استيراد نسخة احتياطية من الملفات المضغوطة أو ملفات SQLite مباشرة"""
        try:
            # فتح حوار اختيار الملف في مجلد النسخ الاحتياطية على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            backup_folder = os.path.join(main_folder, "النسخ الاحتياطي للبرنامج")

            # إنشاء المجلدات إذا لم تكن موجودة
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف النسخة الاحتياطية",
                backup_folder,
                "جميع ملفات النسخ الاحتياطية (*.zip *.sqlite *.db);;ملفات مضغوطة (*.zip);;ملفات قواعد بيانات (*.sqlite *.db)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # فحص نوع الملف المختار
            is_zip = file_path.lower().endswith('.zip')
            is_sqlite = file_path.lower().endswith(('.sqlite', '.db'))

            if not (is_zip or is_sqlite):
                self.show_status_message("الملف المختار ليس ملف نسخة احتياطية معتمد. يرجى اختيار ملف بامتداد ZIP أو SQLite.", "error")
                return

            # عرض رسالة تأكيد للمستخدم
            confirm_dialog = QMessageBox(self)
            confirm_dialog.setWindowTitle("تأكيد استعادة النسخة الاحتياطية")
            confirm_dialog.setText("هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟")
            confirm_dialog.setInformativeText("ستتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.")
            confirm_dialog.setIcon(QMessageBox.Warning)
            confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm_dialog.button(QMessageBox.Yes).setText("نعم")
            confirm_dialog.button(QMessageBox.No).setText("لا")
            confirm_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # التحقق من رغبة المستخدم في المتابعة
            if confirm_dialog.exec_() != QMessageBox.Yes:
                return

            # إنشاء مؤشر تقدم العملية
            progress = QProgressDialog("جاري استعادة النسخة الاحتياطية...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استعادة النسخة الاحتياطية")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setMinimumDuration(0)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    text-align: center;
                    background-color: #ecf0f1;
                }
                QProgressBar::chunk {
                    background-color: #9b59b6;
                    width: 10px;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            # إظهار مؤشر التقدم
            progress.setValue(0)
            progress.show()

            backup_file_path = None
            temp_dir = None

            # معالجة حسب نوع الملف
            if is_zip:
                # 1. استخراج النسخة الاحتياطية من الملف المضغوط إلى مجلد مؤقت
                progress.setValue(10)
                progress.setLabelText("جاري فحص الملف المضغوط...")

                # التحقق من صحة الملف المضغوط
                try:
                    with zipfile.ZipFile(file_path, 'r') as zip_ref:
                        # التأكد من أن الملف يحتوي على ملف النسخة الاحتياطية
                        file_list = zip_ref.namelist()
                        backup_files = [f for f in file_list if f.endswith(('.sqlite', '.db'))]

                        if not backup_files:
                            self.show_status_message("الملف المختار لا يحتوي على نسخة احتياطية صالحة", "error")
                            progress.close()
                            return

                        # استخراج الملف إلى مجلد مؤقت
                        temp_dir = tempfile.mkdtemp()
                        progress.setValue(30)
                        progress.setLabelText("جاري استخراج النسخة الاحتياطية...")

                        zip_ref.extract(backup_files[0], temp_dir)
                        backup_file_path = os.path.join(temp_dir, backup_files[0])
                except Exception as e:
                    self.show_status_message(f"خطأ في استخراج الملف: {str(e)}", "error")
                    progress.close()
                    return
            else:  # ملف SQLite مباشر
                # في حالة كان الملف قاعدة بيانات مباشرة
                backup_file_path = file_path
                progress.setValue(30)
                progress.setLabelText("تم تحديد ملف قاعدة البيانات...")

            # 2. فحص النسخة الاحتياطية
            progress.setValue(50)
            progress.setLabelText("جاري التحقق من النسخة الاحتياطية...")

            try:
                # التحقق من صحة قاعدة البيانات
                test_conn = sqlite3.connect(backup_file_path)
                test_cursor = test_conn.cursor()

                # التحقق من وجود الجداول الأساسية
                test_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in test_cursor.fetchall()]

                # الحد الأدنى من الجداول المتوقعة
                essential_tables = ["بيانات_المؤسسة"]

                if not all(table in tables for table in essential_tables):
                    self.show_status_message("النسخة الاحتياطية غير صالحة: لا تحتوي على جميع الجداول الأساسية", "error")
                    test_conn.close()
                    progress.close()
                    # تنظيف المجلد المؤقت
                    if temp_dir:
                        shutil.rmtree(temp_dir, ignore_errors=True)
                    return

                test_conn.close()
            except Exception as e:
                self.show_status_message(f"خطأ في فحص النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

            # 3. استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
            progress.setValue(70)
            progress.setLabelText("جاري استبدال قاعدة البيانات...")

            try:
                # إغلاق أي اتصالات مفتوحة بقاعدة البيانات
                # على المستخدم إغلاق جميع النوافذ المفتوحة قبل الاستعادة

                # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستبدال
                current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
                pre_restore_backup = f"pre_restore_backup_{current_time}.db"
                pre_restore_path = os.path.join(
                    os.path.dirname(os.path.abspath(self.db_path)),
                    "Backups",
                    pre_restore_backup
                )

                # التأكد من وجود مجلد النسخ الاحتياطية
                backup_folder = os.path.join(os.path.dirname(os.path.abspath(self.db_path)), "Backups")
                if not os.path.exists(backup_folder):
                    os.makedirs(backup_folder)

                # نسخ قاعدة البيانات الحالية
                shutil.copy2(self.db_path, pre_restore_path)

                # استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
                shutil.copy2(backup_file_path, self.db_path)

                progress.setValue(90)
                progress.setLabelText("تم استعادة النسخة الاحتياطية بنجاح!")

                # تنظيف المجلد المؤقت إن وجد
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)

                # إكمال العملية
                progress.setValue(100)

                # عرض رسالة نجاح
                file_name = os.path.basename(file_path)
                success_message = (
                    f"تمت استعادة النسخة الاحتياطية بنجاح!\n\n"
                    f"اسم الملف المستعاد: {file_name}\n"
                    f"تم حفظ نسخة من قاعدة البيانات السابقة في:\n"
                    f"{pre_restore_backup}\n\n"
                    f"يرجى إعادة تشغيل البرنامج لتطبيق التغييرات."
                )
                self.show_status_message(success_message, "success")

                # إغلاق مؤشر التقدم
                progress.close()

            except Exception as e:
                self.show_status_message(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}", "error")
                progress.close()
                # تنظيف المجلد المؤقت
                if temp_dir:
                    shutil.rmtree(temp_dir, ignore_errors=True)
                return

        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء استعادة النسخة الاحتياطية: {str(e)}", "error")

    def not_implemented(self):
        """
        ميزة حماية مجلد البرنامج برمز سري
        تمكن المستخدم من:
        1. اختيار مجلد البرنامج المراد حمايته
        2. إعداد رمز سري قوي للحماية
        3. تطبيق الحماية على المجلد المحدد
        4. حفظ معلومات الحماية في قاعدة البيانات
        """
        self.secure_program_folder()
        
    def secure_program_folder(self):
        """فتح نافذة لاختيار مجلد البرنامج وحمايته برمز سري"""
        try:
            # إنشاء نافذة اختيار المجلد
            folder_dialog = FolderSecurityDialog(self)
            if folder_dialog.exec_() == folder_dialog.Accepted:
                self.show_status_message("تم تأمين المجلد بنجاح برمز سري", "success")
            
        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء تأمين المجلد: {str(e)}", "error")

    def show_database_statistics(self):
        """عرض إحصائيات قاعدة البيانات الفعلية"""
        try:
            # الاتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()
            
            # الحصول على السنة الدراسية
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            academic_year = result[0] if result else "غير محدد"
            
            # إحصائيات الجداول الرئيسية
            table_stats = []
            
            # قائمة الجداول المراد فحصها
            tables_to_check = [
                ("السجل_العام", "التلاميذ المسجلين"),
                ("اللوائح", "اللوائح والأقسام"),
                ("الأساتذة", "الأساتذة"),
                ("البنية_التربوية", "البنية التربوية"),
                ("ورقة_السماح_بالدخول", "أوراق السماح"),
                ("تبريرات_الغياب", "تبريرات الغياب"),
                ("المخالفات", "المخالفات"),
                ("الشهادة_المدرسية", "طلبات الشهادات"),
                ("مسك_الغياب_الأسبوعي", "سجلات الغياب الأسبوعي"),
                ("زيارة_ولي_الأمر", "زيارات أولياء الأمور"),
                ("اخبار_بنشاط", "الأخبار والأنشطة")
            ]
            
            total_records = 0
            for table_name, description in tables_to_check:
                try:
                    # التحقق من وجود الجدول
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
                    if cursor.fetchone():
                        # حساب عدد السجلات
                        cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
                        count = cursor.fetchone()[0]
                        table_stats.append({
                            "table": table_name,
                            "description": description,
                            "count": count,
                            "exists": True
                        })
                        total_records += count
                    else:
                        table_stats.append({
                            "table": table_name,
                            "description": description,
                            "count": 0,
                            "exists": False
                        })
                except Exception as e:
                    table_stats.append({
                        "table": table_name,
                        "description": description,
                        "count": 0,
                        "exists": False,
                        "error": str(e)
                    })
            
            # حساب حجم قاعدة البيانات
            import os
            db_size_bytes = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            db_size_mb = db_size_bytes / (1024 * 1024)
            db_size_kb = db_size_bytes / 1024
            
            # معلومات إضافية
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            total_tables = cursor.fetchone()[0]
            
            # إنشاء محتوى HTML
            html_content = f"""
            <div style="font-family: 'Calibri'; text-align: right; direction: rtl;">
                <h2 style="color: #1abc9c; text-align: center; font-family: 'Calibri'; font-size: 22px; font-weight: bold;">📊 إحصائيات قاعدة البيانات</h2>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="color: #1e3a8a; margin-top: 0; font-family: 'Calibri'; font-size: 20px; font-weight: bold;">📋 معلومات عامة</h3>
                    <ul style="font-family: 'Calibri'; font-size: 17px; color: #1f2937; font-weight: bold; line-height: 1.6;">
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">السنة الدراسية:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{academic_year}</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">إجمالي عدد الجداول:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{total_tables}</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">إجمالي السجلات:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{total_records:,}</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">حجم قاعدة البيانات:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{db_size_mb:.2f} MB ({db_size_kb:.0f} KB)</span></li>
                        <li><span style="color: #1e3a8a; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">مسار قاعدة البيانات:</span> <span style="color: #1f2937; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">{self.db_path}</span></li>
                    </ul>
                </div>
                
                <h3 style="color: #1e3a8a; font-family: 'Calibri'; font-size: 20px; font-weight: bold;">📈 إحصائيات الجداول</h3>
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                    <tr style="background-color: #1abc9c; color: white;">
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: right; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">الجدول</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: right; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">الوصف</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">عدد السجلات</th>
                        <th style="padding: 12px; border: 1px solid #ddd; text-align: center; font-family: 'Calibri'; font-size: 17px; font-weight: bold;">الحالة</th>
                    </tr>
            """
            
            # إضافة صفوف الجدول
            for i, stat in enumerate(table_stats):
                bg_color = "#f9f9f9" if i % 2 == 0 else "white"
                
                if stat["exists"]:
                    status_text = "✅ موجود"
                    status_color = "#2ecc71"
                    count_text = f"{stat['count']:,}"
                else:
                    status_text = "❌ غير موجود"
                    status_color = "#e74c3c"
                    count_text = "-"
                
                html_content += f"""
                    <tr style="background-color: {bg_color};">
                        <td style="padding: 10px; border: 1px solid #ddd; font-family: 'Calibri'; font-size: 17px; font-weight: bold; color: #1f2937;">{stat['table']}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; font-family: 'Calibri'; font-size: 17px; font-weight: bold; color: #1f2937;">{stat['description']}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; text-align: center; font-family: 'Calibri'; font-size: 17px; font-weight: bold; color: #1f2937;">{count_text}</td>
                        <td style="padding: 10px; border: 1px solid #ddd; color: {status_color}; font-family: 'Calibri'; font-size: 17px; font-weight: bold; text-align: center;">{status_text}</td>
                    </tr>
                """
            
            # إغلاق الجدول وإضافة معلومات إضافية
            html_content += """
                </table>
                
                <div style="background-color: #e8f6f3; padding: 15px; border-radius: 10px; margin-top: 20px;">
                    <h3 style="color: #1e3a8a; margin-top: 0; font-family: 'Calibri'; font-size: 20px; font-weight: bold;">💡 ملاحظات مهمة</h3>
                    <ul style="font-family: 'Calibri'; font-size: 17px; color: #1f2937; font-weight: bold; line-height: 1.6;">
                        <li>السجلات الفارغة (0) قد تشير إلى جداول جديدة أو تم تفريغها</li>
                        <li>الجداول غير الموجودة سيتم إنشاؤها تلقائياً عند الحاجة</li>
                        <li>هذه الإحصائيات تعكس حالة قاعدة البيانات الحالية</li>
                        <li>لتحديث الإحصائيات، أعد فتح هذه النافذة</li>
                    </ul>
                </div>
            </div>
            """
            
            conn.close()
            
            # عرض النافذة
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QIcon
            
            dialog = QDialog(self)
            dialog.setWindowTitle("إحصائيات قاعدة البيانات")
            dialog.setMinimumSize(900, 700)
            dialog.setLayoutDirection(Qt.RightToLeft)
            
            # إضافة أيقونة البرنامج
            icon_path = "01.ico"
            if os.path.exists(icon_path):
                dialog.setWindowIcon(QIcon(icon_path))
            
            layout = QVBoxLayout(dialog)
            
            # متصفح النص
            text_browser = QTextBrowser()
            text_browser.setHtml(html_content)
            text_browser.setStyleSheet("""
                QTextBrowser {
                    background-color: white;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
            """)
            layout.addWidget(text_browser)
            
            # أزرار التحكم
            buttons_layout = QHBoxLayout()
            
            # زر التحديث
            refresh_button = QPushButton("🔄 تحديث الإحصائيات")
            refresh_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px;
                    font-family: 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                    min-width: 150px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            refresh_button.clicked.connect(lambda: [dialog.accept(), self.show_database_statistics()])
            
            # زر الإغلاق
            close_button = QPushButton("إغلاق")
            close_button.setStyleSheet("""
                QPushButton {
                    background-color: #1abc9c;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 10px;
                    font-family: 'Calibri';
                    font-size: 14pt;
                    font-weight: bold;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #16a085;
                }
            """)
            close_button.clicked.connect(dialog.accept)
            
            buttons_layout.addWidget(refresh_button)
            buttons_layout.addStretch()
            buttons_layout.addWidget(close_button)
            
            layout.addLayout(buttons_layout)
            
            dialog.exec_()
            
        except Exception as e:
            self.show_status_message(f"خطأ في عرض إحصائيات قاعدة البيانات: {str(e)}", "error")

    def show_libraries_status(self):
        """عرض حالة المكتبات المستخدمة في البرنامج"""
        # إنشاء قائمة بالمكتبات وحالتها
        libraries = [
            {"name": "pandas", "status": PANDAS_AVAILABLE, "description": "للتعامل مع البيانات وملفات Excel (أساسي)"},
            {"name": "xlrd", "status": XLRD_AVAILABLE, "description": "للتعامل مع ملفات Excel القديمة (اختياري)"},
            {"name": "openpyxl", "status": OPENPYXL_AVAILABLE, "description": "للتعامل مع ملفات Excel الحديثة (اختياري)"},
            {"name": "sqlite3", "status": True, "description": "لقاعدة البيانات المحلية (مدمج مع Python)"},
            {"name": "PyQt5", "status": True, "description": "لواجهة المستخدم الرسومية (مثبت بالفعل)"}
        ]

        # إنشاء نص HTML لعرض حالة المكتبات
        html_content = """
        <div style="font-family: 'Calibri'; text-align: right; direction: rtl;">
            <h2 style="color: #1abc9c; text-align: center;">حالة المكتبات المستخدمة في البرنامج</h2>
            <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                <tr style="background-color: #1abc9c; color: white;">
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">اسم المكتبة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الحالة</th>
                    <th style="padding: 10px; border: 1px solid #ddd; text-align: right;">الوصف</th>
                </tr>
        """

        # إضافة صفوف الجدول
        for i, lib in enumerate(libraries):
            bg_color = "#f9f9f9" if i % 2 == 0 else "white"
            status_text = "✅ متوفر" if lib["status"] else "❌ غير متوفر"
            status_color = "#2ecc71" if lib["status"] else "#e74c3c"

            html_content += f"""
                <tr style="background-color: {bg_color};">
                    <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">{lib["name"]}</td>
                    <td style="padding: 10px; border: 1px solid #ddd; color: {status_color}; font-weight: bold;">{status_text}</td>
                    <td style="padding: 10px; border: 1px solid #ddd;">{lib["description"]}</td>
                </tr>
            """

        # إضافة معلومات إضافية
        html_content += """
            </table>
            <div style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                <h3 style="color: #3498db;">معلومات النظام:</h3>
                <ul>
        """

        # إضافة معلومات النظام
        html_content += f"<li><strong>إصدار Python:</strong> {sys.version.split()[0]}</li>"
        html_content += f"<li><strong>نظام التشغيل:</strong> {sys.platform}</li>"
        html_content += f"<li><strong>مسار Python:</strong> {sys.executable}</li>"

        # إضافة نصائح للتثبيت
        html_content += """
                </ul>
                <h3 style="color: #3498db; margin-top: 15px;">نصائح للتثبيت:</h3>
                <ul>
                    <li>لتثبيت pandas: <code>pip install pandas</code></li>
                    <li>لتثبيت xlrd: <code>pip install xlrd</code></li>
                    <li>لتثبيت openpyxl: <code>pip install openpyxl</code></li>
                </ul>
                <p style="margin-top: 10px; font-style: italic;">ملاحظة: مكتبة pandas هي الأساسية للتعامل مع ملفات Excel. المكتبات الأخرى اختيارية وتساعد في دعم أنواع مختلفة من ملفات Excel.</p>
            </div>
        </div>
        """

        # عرض النافذة مع المعلومات
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton
        from PyQt5.QtCore import Qt

        dialog = QDialog(self)
        dialog.setWindowTitle("حالة المكتبات")
        dialog.setMinimumSize(800, 600)
        dialog.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout(dialog)

        text_browser = QTextBrowser()
        text_browser.setHtml(html_content)
        layout.addWidget(text_browser)

        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #1abc9c;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Calibri';
                font-size: 14pt;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #16a085;
            }
        """)
        close_button.clicked.connect(dialog.accept)

        layout.addWidget(close_button, alignment=Qt.AlignCenter)

        # إضافة أيقونة البرنامج
        icon_path = "01.ico"
        if os.path.exists(icon_path):
            from PyQt5.QtGui import QIcon
            dialog.setWindowIcon(QIcon(icon_path))

        dialog.exec_()

    def import_excel_data(self, excel_file_path, sheet_name=None):
        """استيراد بيانات من ملف Excel باستخدام pandas"""
        try:
            # التحقق من توفر pandas
            if not PANDAS_AVAILABLE:
                return None, "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر: pip install pandas"

            # التحقق من وجود الملف
            if not os.path.exists(excel_file_path):
                return None, f"الملف غير موجود: {excel_file_path}"

            # التحقق من امتداد الملف
            if not excel_file_path.lower().endswith(('.xlsx', '.xls')):
                return None, f"الملف ليس ملف Excel صالح: {excel_file_path}"

            try:
                # قراءة أسماء الأوراق في ملف Excel
                xls = pd.ExcelFile(excel_file_path)

                # إذا لم يتم تحديد اسم الورقة، إرجاع قائمة الأوراق
                if not sheet_name:
                    return xls.sheet_names, "تم استرجاع قائمة الأوراق بنجاح"

                # قراءة الورقة المحددة
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)

                # تحويل البيانات إلى قاموس
                data = df.to_dict('records')

                return data, f"تم استيراد البيانات بنجاح من ورقة {sheet_name}"

            except Exception as e:
                return None, f"خطأ في قراءة ملف Excel: {str(e)}"

        except Exception as e:
            return None, f"خطأ عام في استيراد البيانات: {str(e)}"

    def insert_weekly_absence(self):
        """إدراج الغياب الأسبوعي داخل البرنامج"""
        try:
            # الاتصال بقاعدة البيانات
            conn = get_database_connection()
            cursor = conn.cursor()

            # الحصول على السنة الدراسية من جدول بيانات_المؤسسة
            cursor.execute("SELECT السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
            result = cursor.fetchone()
            if not result:
                self.show_status_message("لم يتم العثور على بيانات في جدول بيانات_المؤسسة", "error")
                return
            السنة = result[0]

            # باستخدام تعبير جدول مشترك (CTE) مع دالة النافذة، نختار 10 سجلات فقط من جدول جدولة_مسك_الغياب
            # بعد تصفية السجلات حسب السنة الدراسية
            # ثم نقوم بعمل Cross Join مع سجلات التلاميذ من جدول اللوائح (بعد التصفية بنفس السنة)
            insert_query = """
            WITH limited_schedule AS (
                SELECT *
                FROM (
                    SELECT j.*, ROW_NUMBER() OVER (ORDER BY j.الشهر) as rn
                    FROM جدولة_مسك_الغياب j
                    WHERE j.السنة_الدراسية = ?
                ) sub
                WHERE rn <= 10
            )
            INSERT OR IGNORE INTO مسك_الغياب_الأسبوعي
                (السنة_الدراسية, ملاحظات,الأسدس, الشهر, "1", "2", "3", "4", "5", بداية_الشهر, رمز_التلميذ)
            SELECT
                ls.السنة_الدراسية,
                '' as "ملاحظات",
                ls.الأسدس,
                ls.الشهر,
                '0' as "1",
                '0' as "2",
                '0' as "3",
                '0' as "4",
                '0' as "5",

                ls.بداية_الشهر,
                l.الرمز
            FROM limited_schedule ls
            CROSS JOIN (SELECT * FROM اللوائح WHERE السنة_الدراسية = ?) l
            """
            # تمرير قيمة السنة لكل من تصفية جدول جدولة_مسك_الغياب وجدول اللوائح
            cursor.execute(insert_query, (السنة, السنة))
            conn.commit()

            # حساب عدد السجلات في جدول مسك_الغياب_الأسبوعي بعد الإدراج
            cursor.execute("SELECT COUNT(*) FROM مسك_الغياب_الأسبوعي")
            count = cursor.fetchone()[0]

            self.show_status_message(f"تم إدراج الغياب الأسبوعي داخل البرنامج بنجاح!\nعدد السجلات الحالي: {count}", "success")
        except Exception as e:
            self.show_status_message(f"حدث خطأ أثناء الإدراج: {e}", "error")
        finally:
            if conn:
                conn.close()

    def import_phone_numbers(self):
        """
        استيراد أرقام الهواتف من النسخة السابقة (ملف إكسل)
        
        طريقتان للاستيراد:
        1. الطريقة القديمة: البحث في أسماء الأعمدة
        2. الطريقة الجديدة: استخدام مواقع الأعمدة المحددة:
           - عمود B = الرمز
           - عمود H = الرمز_السري  
           - عمود I = الهاتف_الأول
           - عمود J = الهاتف_الثاني
           - عمود N = ملاحظات
        """
        try:
            # 1. التحقق من توفر مكتبة pandas
            if not PANDAS_AVAILABLE:
                self.show_status_message(
                    "مكتبة pandas غير متوفرة. الرجاء تثبيتها باستخدام الأمر:\n\npip install pandas",
                    "error"
                )
                return

            # 1.5. اختيار طريقة الاستيراد
            from PyQt5.QtWidgets import QInputDialog
            import_methods = [
                "استخدام أسماء الأعمدة (الطريقة القديمة)",
                "استخدام مواقع الأعمدة المحددة (B, H, I, J, N)"
            ]
            
            import_method, ok = QInputDialog.getItem(
                self,
                "اختيار طريقة الاستيراد",
                "اختر طريقة استيراد البيانات:",
                import_methods,
                1,  # الطريقة الجديدة كافتراضية
                False
            )
            
            if not ok:
                return
            
            use_column_positions = "مواقع الأعمدة المحددة" in import_method

            # 2. فتح حوار اختيار ملف إكسل
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف إكسل للبيانات السابقة",
                "",
                "ملفات إكسل (*.xlsx *.xls);;جميع الملفات (*.*)"
            )

            if not file_path:
                # المستخدم ألغى العملية
                return

            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                self.show_status_message(
                    f"الملف المحدد غير موجود:\n{file_path}",
                    "error"
                )
                return

            # 3. إنشاء مؤشر تقدم العملية
            progress = QProgressDialog("جاري استيراد بيانات الهواتف...", "إلغاء", 0, 100, self)
            progress.setWindowTitle("استيراد أرقام الهواتف")
            progress.setWindowModality(Qt.WindowModal)
            progress.setAutoClose(True)
            progress.setStyleSheet("""
                QProgressDialog {
                    background-color: white;
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: black;
                }
                QProgressBar {
                    border: 1px solid #bdc3c7;
                    border-radius: 3px;
                    text-align: center;
                }
                QProgressBar::chunk {
                    background-color: #2ecc71;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)
            progress.show()
            progress.setValue(10)

            try:
                # طباعة مسار الملف للتشخيص
                print(f"مسار ملف إكسيل: {file_path}")
                progress.setLabelText(f"جاري فحص الملف: {os.path.basename(file_path)}")

                # 4. محاولة قراءة ورقات ملف إكسيل
                try:
                    # قراءة أسماء الورقات في ملف إكسيل
                    import pandas as pd
                    xls = pd.ExcelFile(file_path)
                    sheet_names = xls.sheet_names
                    print(f"الورقات الموجودة في الملف: {sheet_names}")
                except Exception as e:
                    error_msg = f"خطأ في قراءة ورقات ملف إكسيل: {str(e)}"
                    print(error_msg)
                    self.show_status_message(error_msg, "error")
                    progress.close()
                    return

                if not sheet_names:
                    self.show_status_message(
                        "لم يتم العثور على أي ورقات في ملف إكسيل المحدد.",
                        "error"
                    )
                    progress.close()
                    return

                # إذا كان هناك أكثر من ورقة، اسأل المستخدم عن الورقة المطلوبة
                target_sheet = None
                if len(sheet_names) > 1:
                    # البحث عن ورقة قد تحتوي على بيانات الطلاب
                    for sheet in sheet_names:
                        if 'سجل' in sheet or 'طلاب' in sheet or 'تلاميذ' in sheet:
                            target_sheet = sheet
                            break

                    # إذا لم نجد ورقة مناسبة، نسأل المستخدم عن الورقة المطلوبة
                    if not target_sheet:
                        from PyQt5.QtWidgets import QInputDialog
                        target_sheet, ok = QInputDialog.getItem(
                            self,
                            "اختيار ورقة البيانات",
                            "اختر الورقة التي تحتوي على بيانات الطلاب:",
                            sheet_names,
                            0,
                            False
                        )
                        if not ok or not target_sheet:
                            self.show_status_message("لم يتم اختيار ورقة بيانات. تم إلغاء العملية.", "info")
                            progress.close()
                            return
                else:
                    # إذا كانت هناك ورقة واحدة فقط، استخدمها
                    target_sheet = sheet_names[0]

                progress.setValue(30)
                progress.setLabelText(f"جاري قراءة البيانات من ورقة {target_sheet}...")

                # 5. قراءة ورقة البيانات مباشرة إلى DataFrame باستخدام pandas
                try:
                    # تعيين نوع البيانات للأعمدة المستهدفة كنصوص لضمان عدم فقدان البيانات
                    dtype_dict = {'الهاتف_الأول': str, 'الهاتف_الثاني': str, 'رقم الهاتف الأول': str, 'رقم الهاتف الثاني': str}

                    # محاولة قراءة الملف مع تحديد نوع البيانات
                    df = pd.read_excel(
                        file_path,
                        sheet_name=target_sheet,
                        dtype=dtype_dict,  # تحديد أنواع البيانات للأعمدة الأساسية
                        keep_default_na=False,  # عدم تحويل القيم الفارغة إلى NaN
                        na_values=["#N/A", "N/A", "NA"]  # تحديد قيم NA المعترف بها
                    )

                    print(f"تم قراءة {len(df)} سجل من الورقة {target_sheet}")
                    print(f"أسماء الأعمدة: {df.columns.tolist()}")
                except Exception as e:
                    error_msg = f"خطأ في قراءة ورقة {target_sheet}: {str(e)}"
                    print(error_msg)
                    self.show_status_message(error_msg, "error")
                    progress.close()
                    return

                # 6. تحديد الأعمدة المطلوبة
                progress.setValue(50)
                progress.setLabelText("جاري تحليل بيانات الجدول...")

                columns_map = {}
                
                if use_column_positions:
                    # استخدام مواقع الأعمدة المحددة مسبقاً
                    print("استخدام مواقع الأعمدة المحددة:")
                    
                    # تحويل أحرف الأعمدة إلى فهارس (B=1, H=7, I=8, J=9, N=13)
                    column_positions = {
                        'الرمز': 1,           # عمود B
                        'الرمز_السري': 7,     # عمود H  
                        'الهاتف_الأول': 8,     # عمود I
                        'الهاتف_الثاني': 9,    # عمود J
                        'ملاحظات': 13         # عمود N
                    }
                    
                    # التحقق من وجود الأعمدة المطلوبة
                    for field_name, col_index in column_positions.items():
                        if col_index < len(df.columns):
                            columns_map[field_name] = df.columns[col_index]
                            print(f"تم تعيين {field_name} للعمود {df.columns[col_index]} (فهرس {col_index})")
                        else:
                            print(f"تحذير: العمود {col_index} غير موجود للحقل {field_name}")
                            
                else:
                    # الطريقة القديمة: البحث بأسماء الأعمدة
                    possible_column_names = {
                        'الرمز': ['الرمز', 'رمز', 'رمز التلميذ', 'رمز الطالب', 'رقم التسجيل', 'id', 'code'],
                        'الهاتف_الأول': ['الهاتف_الأول', 'الهاتف الأول', 'رقم الهاتف الأول', 'الهاتف 1', 'phone1', 'هاتف1', 'هاتف 1', 'هاتف أول'],
                        'الهاتف_الثاني': ['الهاتف_الثاني', 'الهاتف الثاني', 'رقم الهاتف الثاني', 'الهاتف 2', 'phone2', 'هاتف2', 'هاتف 2', 'هاتف ثاني'],
                        'ملاحظات': ['ملاحظات', 'notes', 'ملاحظة', 'notes'],
                        'الرمز_السري': ['الرمز_السري', 'الرمز السري', 'كلمة المرور', 'password']
                    }

                    # البحث عن الأعمدة باستخدام القاموس
                    for req_col, possible_names in possible_column_names.items():
                        for col in df.columns:
                            col_str = str(col).lower().strip()
                            if any(name.lower() in col_str for name in possible_names):
                                columns_map[req_col] = col
                                print(f"تم مطابقة العمود {req_col} مع {col}")
                                break

                # التحقق من وجود عمود الرمز على الأقل
                if 'الرمز' not in columns_map:
                    if use_column_positions:
                        self.show_status_message(
                            "العمود B (الرمز) غير موجود في الملف أو الملف لا يحتوي على عدد كافٍ من الأعمدة.\n"
                            "تأكد من أن الملف يحتوي على الأعمدة المطلوبة.",
                            "error"
                        )
                        progress.close()
                        return
                    else:
                        # عرض أسماء الأعمدة الموجودة وأطلب من المستخدم تحديد عمود الرمز
                        from PyQt5.QtWidgets import QInputDialog
                        code_column, ok = QInputDialog.getItem(
                            self,
                            "تحديد عمود الرمز",
                            "اختر العمود الذي يحتوي على رمز الطلاب:",
                            [str(col) for col in df.columns],
                            0,
                            False
                        )
                        if ok and code_column:
                            columns_map['الرمز'] = code_column
                        else:
                            self.show_status_message("لم يتم تحديد عمود الرمز. تم إلغاء العملية.", "info")
                            progress.close()
                            return

                # عرض خريطة الأعمدة المستخدمة
                print("\n--- خريطة الأعمدة المستخدمة ---")
                for field, column in columns_map.items():
                    print(f"{field}: {column}")
                print("-------------------------------\n")

                # 7. معالجة البيانات واستيراد من إكسيل إلى قاعدة البيانات SQLite المحلية
                # الاتصال بقاعدة بيانات SQLite المحلية
                conn_sqlite = get_database_connection()
                cursor_sqlite = conn_sqlite.cursor()

                # الحصول على قائمة بالتلاميذ الموجودين في قاعدة البيانات الحالية
                cursor_sqlite.execute("SELECT الرمز FROM السجل_العام")
                existing_students = {row[0] for row in cursor_sqlite.fetchall()}
                print(f"عدد التلاميذ الموجودين في قاعدة البيانات الحالية: {len(existing_students)}")

                # معالجة البيانات وتحديث قاعدة البيانات
                records_updated = 0
                records_failed = 0
                records_not_found = 0

                # إعداد المعاملة
                conn_sqlite.execute('BEGIN TRANSACTION')

                # أسماء الأعمدة في DataFrame
                code_col = columns_map.get('الرمز')
                phone1_col = columns_map.get('الهاتف_الأول', '')
                phone2_col = columns_map.get('الهاتف_الثاني', '')
                notes_col = columns_map.get('ملاحظات', '')
                secret_col = columns_map.get('الرمز_السري', '')

                progress.setValue(60)
                progress.setLabelText("جاري تحديث بيانات الهواتف...")

                # معالجة كل صف في DataFrame
                total_rows = len(df)
                for i, row in df.iterrows():
                    if progress.wasCanceled():
                        conn_sqlite.rollback()
                        conn_sqlite.close()
                        progress.close()
                        return

                    try:
                        # الحصول على قيمة الرمز
                        code = str(row[code_col]).strip() if pd.notna(row[code_col]) else None

                        # تجاهل السجلات بدون رمز
                        if code is None or code == '':
                            continue

                        # تنظيف الرمز من أي مسافات أو أحرف غريبة
                        code = code.strip()

                        # التحقق من وجود الرمز في قاعدة البيانات الحالية
                        if code in existing_students:
                            # الحصول على البيانات وتأكد من معالجتها كنصوص
                            # استخدم str() لضمان التحويل إلى نص وتأكد من عدم وجود قيم NaN

                            # الهاتف الأول
                            if phone1_col and pd.notna(row.get(phone1_col, '')):
                                phone1 = str(row[phone1_col])
                                # تنظيف البيانات من القيم غير المرغوبة
                                phone1 = phone1.strip().replace('nan', '').replace('None', '')
                            else:
                                phone1 = ""

                            # الهاتف الثاني
                            if phone2_col and pd.notna(row.get(phone2_col, '')):
                                phone2 = str(row[phone2_col])
                                # تنظيف البيانات من القيم غير المرغوبة
                                phone2 = phone2.strip().replace('nan', '').replace('None', '')
                            else:
                                phone2 = ""

                            # ملاحظات
                            notes = str(row[notes_col]).strip() if notes_col and pd.notna(row.get(notes_col, '')) else ""

                            # الرمز السري
                            secret = str(row[secret_col]).strip() if secret_col and pd.notna(row.get(secret_col, '')) else ""

                            # تحديث البيانات
                            cursor_sqlite.execute("""
                                UPDATE السجل_العام
                                SET الهاتف_الأول = ?, الهاتف_الثاني = ?, ملاحظات = ?, الرمز_السري = ?
                                WHERE الرمز = ?
                            """, (phone1, phone2, notes, secret, code))

                            records_updated += 1

                            # طباعة تفاصيل التحديث للمساعدة في التشخيص
                            if i % 50 == 0:  # طباعة كل 50 سجل فقط لتجنب الكثير من المخرجات
                                print(f"تم تحديث السجل {i}: الرمز={code}, الهاتف الأول={phone1}, الهاتف الثاني={phone2}")
                        else:
                            records_not_found += 1
                            if records_not_found <= 5:  # طباعة أول 5 سجلات غير موجودة فقط
                                print(f"الرمز غير موجود في قاعدة البيانات: {code}")
                    except Exception as e:
                        records_failed += 1
                        print(f"خطأ في تحديث السجل رقم {i} (الرمز: {code}): {str(e)}")

                    # تحديث مؤشر التقدم
                    progress_value = 60 + int((i + 1) / total_rows * 30)
                    progress.setValue(progress_value)
                    if i % 10 == 0:  # تحديث النص كل 10 سجلات فقط لتحسين الأداء
                        progress.setLabelText(f"جاري تحديث بيانات الهواتف... ({i+1}/{total_rows})")

                # حفظ التغييرات
                conn_sqlite.commit()
                conn_sqlite.close()

                progress.setValue(95)

                # عرض تقرير النجاح
                import_method_text = "مواقع الأعمدة المحددة (B, H, I, J, N)" if use_column_positions else "أسماء الأعمدة"
                
                success_message = (
                    f"تم استيراد أرقام الهواتف والبيانات من ملف إكسيل بنجاح!\n\n"
                    f"طريقة الاستيراد: {import_method_text}\n"
                    f"اسم الورقة: {target_sheet}\n"
                    f"إجمالي السجلات: {total_rows}\n"
                    f"السجلات المحدثة: {records_updated}\n"
                    f"السجلات غير الموجودة: {records_not_found}\n"
                    f"السجلات الفاشلة: {records_failed}"
                )

                progress.setValue(100)
                self.show_status_message(success_message, "success")

            except Exception as e:
                error_details = str(e)
                print(f"خطأ في استيراد البيانات: {error_details}")
                self.show_status_message(f"خطأ في استيراد البيانات: {error_details}", "error")
            finally:
                if progress and progress.isVisible():
                    progress.close()

        except Exception as e:
            error_details = str(e)
            print(f"حدث خطأ أثناء استيراد أرقام الهواتف: {error_details}")
            self.show_status_message(f"حدث خطأ أثناء استيراد أرقام الهواتف: {error_details}", "error")

    def unlock_protected_folder(self):
        """فتح مجلد محمي برمز سري"""
        try:
            # اختيار المجلد المحمي
            folder = QFileDialog.getExistingDirectory(
                self,
                "اختر المجلد المحمي المراد فتحه",
                "",
                QFileDialog.ShowDirsOnly
            )
            
            if not folder:
                return
                
            # فحص ما إذا كان المجلد محمياً
            if not self.is_folder_protected(folder):
                QMessageBox.warning(self, "تحذير", "هذا المجلد غير محمي!")
                return
            
            # طلب الرمز السري
            password, ok = QInputDialog.getText(
                self, 
                "أدخل الرمز السري", 
                f"أدخل الرمز السري لفتح المجلد:\n{folder}", 
                QLineEdit.Password
            )
            
            if not ok or not password:
                return
                
            # التحقق من الرمز السري وفتح المجلد
            if self.verify_folder_password(folder, password):
                self.restore_protected_folder(folder)
                QMessageBox.information(
                    self, 
                    "نجح الفتح", 
                    f"تم فتح المجلد بنجاح واستعادة جميع المحتويات!\n\nالمجلد: {folder}"
                )
                self.show_status_message("تم فتح المجلد المحمي بنجاح", "success")
            else:
                QMessageBox.critical(self, "خطأ", "الرمز السري غير صحيح!")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح المجلد:\n{str(e)}")
            self.show_status_message(f"خطأ في فتح المجلد المحمي: {str(e)}", "error")

    def verify_folder_password(self, folder_path, password):
        """التحقق من صحة الرمز السري للمجلد المحمي"""
        try:
            protection_file = os.path.join(folder_path, ".folder_protection")
            if not os.path.exists(protection_file):
                return False
                
            # قراءة الرمز السري المحفوظ
            with open(protection_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            stored_hash = None
            for line in lines:
                if line.startswith('password_hash='):
                    stored_hash = line.split('=', 1)[1].strip()
                    break
                    
            if not stored_hash:
                return False
                
            # مقارنة الرمز السري
            input_hash = hashlib.sha256(password.encode()).hexdigest()
            return input_hash == stored_hash
            
        except Exception as e:
            print(f"خطأ في التحقق من الرمز السري: {e}")
            return False

    def restore_protected_folder(self, folder_path):
        """إزالة حماية المجلد (النظام الجديد - بدون نسخ احتياطية)"""
        try:
            protection_file = os.path.join(folder_path, ".folder_protection")
            
            if not os.path.exists(protection_file):
                raise Exception("هذا المجلد غير محمي أو تم حذف ملف الحماية!")
            
            # قراءة نوع الحماية
            protection_type = 'folder_lock'  # النوع الافتراضي الجديد
            with open(protection_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            for line in lines:
                if line.startswith('protection_type='):
                    protection_type = line.split('=', 1)[1].strip()
                    break
            
            if protection_type == 'folder_lock':
                # النظام الجديد - إزالة الحماية فقط
                self.remove_windows_folder_protection(folder_path)
                
                # حذف ملفات الحماية
                warning_files = [
                    "🔒 مجلد محمي - اقرأ التعليمات.txt",
                    "🔓 فتح البرنامج.lnk",
                    "🔓 فتح البرنامج.bat"
                ]
                
                for warning_file in warning_files:
                    warning_path = os.path.join(folder_path, warning_file)
                    if os.path.exists(warning_path):
                        try:
                            os.remove(warning_path)
                        except:
                            pass
                
                # حذف ملف الحماية
                try:
                    os.system(f'attrib -h -s "{protection_file}"')  # إزالة الإخفاء أولاً
                    os.remove(protection_file)
                except Exception as e:
                    print(f"تحذير: فشل في حذف ملف الحماية: {e}")
                
            else:
                # النظام القديم - استعادة من النسخة الاحتياطية
                backup_folder = None
                for line in lines:
                    if line.startswith('backup_location='):
                        backup_folder = line.split('=', 1)[1].strip()
                        break
                        
                if not backup_folder or not os.path.exists(backup_folder):
                    raise Exception("لم يتم العثور على النسخة الاحتياطية للمجلد!")
                
                # حذف المحتويات الحالية (عدا ملف الحماية)
                for item in os.listdir(folder_path):
                    if item.startswith('.folder_protection'):
                        continue
                        
                    item_path = os.path.join(folder_path, item)
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                    else:
                        os.remove(item_path)
                
                # استعادة المحتويات من النسخة الاحتياطية
                for item in os.listdir(backup_folder):
                    src = os.path.join(backup_folder, item)
                    dst = os.path.join(folder_path, item)
                    
                    if os.path.isdir(src):
                        shutil.copytree(src, dst)
                    else:
                        shutil.copy2(src, dst)
                
                # حذف النسخة الاحتياطية
                try:
                    subprocess.run(['attrib', '-h', '-s', backup_folder], 
                                 capture_output=True, shell=True)
                    shutil.rmtree(backup_folder)
                except Exception as e:
                    print(f"تحذير: فشل في حذف النسخة الاحتياطية: {e}")
                
                # حذف ملف الحماية
                try:
                    subprocess.run(['attrib', '-h', '-s', protection_file], 
                                 capture_output=True, shell=True)
                    os.remove(protection_file)
                except Exception as e:
                    print(f"تحذير: فشل في حذف ملف الحماية: {e}")
            
            # إزالة المجلد من قاعدة البيانات
            self.remove_protected_folder_from_db(folder_path)
            
            print(f"تم فتح المجلد بنجاح: {folder_path}")
            
        except Exception as e:
            raise Exception(f"فشل في فتح المجلد: {str(e)}")
            
            # استعادة المحتويات من النسخة الاحتياطية
            for item in os.listdir(backup_folder):
                src_path = os.path.join(backup_folder, item)
                dst_path = os.path.join(folder_path, item)
                
                if os.path.isdir(src_path):
                    shutil.copytree(src_path, dst_path)
                else:
                    shutil.copy2(src_path, dst_path)
            
            # حذف ملف الحماية والنسخة الاحتياطية
            os.remove(protection_file)
            shutil.rmtree(backup_folder, ignore_errors=True)
            
            # تحديث قاعدة البيانات
            self.remove_protected_folder_from_db(folder_path)
            
            print(f"تم استعادة المجلد بنجاح: {folder_path}")
            
        except Exception as e:
            raise Exception(f"فشل في استعادة المجلد: {str(e)}")

    def remove_protected_folder_from_db(self, folder_path):
        """إزالة معلومات المجلد المحمي من قاعدة البيانات"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute("DELETE FROM protected_folders WHERE folder_path = ?", (folder_path,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"تحذير: لم يتم حذف معلومات الحماية من قاعدة البيانات: {e}")

    def close_window_only(self):
        """إغلاق النافذة فقط بدون إغلاق البرنامج"""
        self.close()


# فئات مساعدة لحماية المجلد
class FolderSecurityDialog(QDialog):
    """نافذة لاختيار مجلد البرنامج وحمايته برمز سري"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔒 تأمين مجلد البرنامج")
        self.setFixedSize(700, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        # متغيرات الحالة
        self.selected_folder = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(25)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # عنوان النافذة
        title_label = QLabel("🔒 تأمين مجلد البرنامج برمز سري")
        title_label.setFont(QFont("Calibri", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 12px;
                border: 3px solid #3498db;
            }
        """)
        main_layout.addWidget(title_label)
        
        # تعليمات المستخدم
        instructions_label = QLabel(
            " ✅ النظام الجديد: إغلاق المجلد برمز سري بدون تشفير الملفات\n"
            " 🔒 سيتم حماية المجلد من التعديل والوصول غير المصرح به\n"
            " ⚡ البرنامج سيعمل بشكل طبيعي حتى مع تطبيق الحماية!"
        )
        instructions_label.setFont(QFont("Calibri", 13))
        instructions_label.setStyleSheet("""
            QLabel {
                color: #00b894;
                padding: 20px;
                background-color: #d1f2eb;
                border-radius: 10px;
                border-left: 5px solid #00b894;
                line-height: 1.4;
            }
        """)
        instructions_label.setWordWrap(True)
        main_layout.addWidget(instructions_label)
        
        # قسم اختيار المجلد
        folder_frame = QFrame()
        folder_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 12px;
                border: 2px solid #dee2e6;
                padding: 15px;
            }
        """)
        folder_layout = QVBoxLayout(folder_frame)
        folder_layout.setSpacing(15)
        
        folder_label = QLabel("📁 اختر مجلد البرنامج المراد حمايته:")
        folder_label.setFont(QFont("Calibri", 15, QFont.Bold))
        folder_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        folder_layout.addWidget(folder_label)
        
        # مسار المجلد المحدد
        self.folder_path_label = QLabel("لم يتم اختيار مجلد بعد...\nانقر على الزر أدناه لتصفح واختيار المجلد المراد حمايته")
        self.folder_path_label.setFont(QFont("Calibri", 12))
        self.folder_path_label.setStyleSheet("""
            QLabel {
                padding: 15px;
                background-color: white;
                border: 2px dashed #ced4da;
                border-radius: 8px;
                color: #6c757d;
                min-height: 60px;
            }
        """)
        self.folder_path_label.setWordWrap(True)
        self.folder_path_label.setAlignment(Qt.AlignCenter)
        folder_layout.addWidget(self.folder_path_label)
        
        # زر اختيار المجلد
        select_folder_btn = QPushButton("🔍 تصفح واختيار المجلد")
        select_folder_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        select_folder_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        select_folder_btn.clicked.connect(self.select_folder)
        folder_layout.addWidget(select_folder_btn)
        
        main_layout.addWidget(folder_frame)
        
        # أزرار العمل
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(20)
        
        # زر المتابعة
        continue_btn = QPushButton("🔐 متابعة إعداد الحماية")
        continue_btn.setFont(QFont("Calibri", 15, QFont.Bold))
        continue_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 18px;
                border-radius: 10px;
                min-height: 50px;
                min-width: 250px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
            QPushButton:pressed {
                background-color: #155724;
            }
        """)
        continue_btn.clicked.connect(self.continue_to_password)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء العملية")
        cancel_btn.setFont(QFont("Calibri", 15, QFont.Bold))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 18px;
                border-radius: 10px;
                min-height: 50px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(continue_btn)
        buttons_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def select_folder(self):
        """فتح نافذة اختيار المجلد مع فحوصات الأمان"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد البرنامج المراد حمايته",
            "",
            QFileDialog.ShowDirsOnly
        )
        
        if folder:
            # فحص الأمان والصلاحيات
            safety_check = self.check_folder_safety(folder)
            if not safety_check['safe']:
                QMessageBox.warning(
                    self, 
                    "تحذير - مجلد غير آمن!", 
                    f"لا يمكن حماية هذا المجلد:\n\n{safety_check['reason']}\n\n"
                    "يرجى اختيار مجلد آخر."
                )
                return
                
            # فحص ما إذا كان المجلد محمياً مسبقاً
            if self.is_folder_protected(folder):
                reply = QMessageBox.question(
                    self, 
                    "مجلد محمي مسبقاً", 
                    f"هذا المجلد محمي مسبقاً برمز سري.\n\n"
                    f"المجلد: {folder}\n\n"
                    "هل تريد إزالة الحماية السابقة وإعادة حمايته برمز جديد؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.No:
                    return
                    
            self.selected_folder = folder
            
            # تحديث النص لإظهار المجلد المحدد
            folder_display = folder if len(folder) < 80 else folder[:77] + "..."
            self.folder_path_label.setText(
                f"✅ تم اختيار المجلد:\n\n{folder_display}\n\n"
                f"📊 إحصائيات المجلد:\n"
                f"• عدد الملفات: {self.count_files(folder)}\n"
                f"• الحجم التقريبي: {self.get_folder_size(folder)}\n\n"
                "✅ المجلد جاهز للحماية - اضغط متابعة"
            )
            self.folder_path_label.setStyleSheet("""
                QLabel {
                    padding: 15px;
                    background-color: #d4edda;
                    border: 2px solid #28a745;
                    border-radius: 8px;
                    color: #155724;
                    min-height: 60px;
                }
            """)
            
    def count_files(self, folder_path):
        """عد الملفات في المجلد"""
        try:
            count = 0
            for root, dirs, files in os.walk(folder_path):
                count += len(files)
                if count > 1000:  # حد أقصى للعرض
                    return "1000+"
            return str(count)
        except:
            return "غير محدد"
    
    def get_folder_size(self, folder_path):
        """حساب حجم المجلد"""
        try:
            total_size = 0
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                    except:
                        continue
            
            # تحويل إلى وحدات مقروءة
            if total_size < 1024:
                return f"{total_size} بايت"
            elif total_size < 1024 * 1024:
                return f"{total_size / 1024:.1f} كيلوبايت"
            elif total_size < 1024 * 1024 * 1024:
                return f"{total_size / (1024 * 1024):.1f} ميجابايت"
            else:
                return f"{total_size / (1024 * 1024 * 1024):.1f} جيجابايت"
        except:
            return "غير محدد"
    
    def check_folder_safety(self, folder_path):
        """فحص أمان المجلد قبل الحماية"""
        try:
            # فحص ما إذا كان مجلد نظام
            system_folders = [
                'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
                'C:\\ProgramData', 'C:\\Users\\<USER>\\System Volume Information'
            ]
            
            folder_path_normalized = os.path.normpath(folder_path).upper()
            for sys_folder in system_folders:
                if folder_path_normalized.startswith(sys_folder.upper()):
                    return {
                        'safe': False, 
                        'reason': f'هذا مجلد نظام مهم ({sys_folder}).\nحماية مجلدات النظام قد تؤثر على استقرار الحاسوب.'
                    }
            
            # فحص الصلاحيات
            if not self.check_folder_permissions(folder_path):
                return {
                    'safe': False,
                    'reason': 'ليس لديك صلاحيات كافية للوصول إلى هذا المجلد.\nيرجى تشغيل البرنامج كمدير أو اختيار مجلد آخر.'
                }
            
            return {'safe': True, 'reason': ''}
            
        except Exception as e:
            return {
                'safe': False,
                'reason': f'حدث خطأ أثناء فحص المجلد:\n{str(e)}'
            }
    
    def check_folder_permissions(self, folder_path):
        """فحص صلاحيات الوصول للمجلد"""
        try:
            # محاولة إنشاء ملف تجريبي
            test_file = os.path.join(folder_path, f"test_permission_{int(time.time())}.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return True
        except (PermissionError, OSError):
            return False
    
    def is_folder_protected(self, folder_path):
        """فحص ما إذا كان المجلد محمياً بالفعل"""
        try:
            protection_file = os.path.join(folder_path, ".folder_protection")
            if not os.path.exists(protection_file):
                return False
                
            # قراءة معلومات الحماية
            with open(protection_file, 'r', encoding='utf-8') as f:
                content = f.read()
                return 'protected=true' in content.lower()
        except:
            return False
            
    def continue_to_password(self):
        """المتابعة لإعداد الرمز السري"""
        if not self.selected_folder:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مجلد أولاً!")
            return
            
        # فتح نافذة إعداد الرمز السري
        password_dialog = PasswordSetupDialog(self.selected_folder, self)
        if password_dialog.exec_() == QDialog.Accepted:
            self.accept()
            
    def reject(self):
        """إلغاء العملية"""
        super().reject()


class PasswordSetupDialog(QDialog):
    """نافذة إعداد الرمز السري لحماية المجلد"""
    def __init__(self, folder_path, parent=None):
        super().__init__(parent)
        self.folder_path = folder_path
        self.setWindowTitle("🔑 إعداد الرمز السري")
        self.setFixedSize(650, 520)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة إدخال الرمز السري"""
        # تعيين خلفية مختلفة للنافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f093fb, stop:1 #f5576c);
            }
        """)
        
        # إنشاء العناصر مباشرة بدون تخطيط رئيسي
        
        # عنوان النافذة
        title_label = QLabel("🔑 إعداد الرمز السري للحماية", self)
        title_label.setFont(QFont("Calibri", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setGeometry(40, 30, 570, 60)
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(46, 204, 113, 0.9);
                border-radius: 12px;
                border: 2px solid #27ae60;
            }
        """)
        
        # عرض المجلد المحدد
        folder_label = QLabel(f"📁 المجلد المحدد للحماية:\n\n{self.folder_path}", self)
        folder_label.setFont(QFont("Calibri", 12))
        folder_label.setGeometry(40, 110, 570, 80)
        folder_label.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 255, 255, 0.95);
                border-radius: 10px;
                border: 2px solid rgba(155, 89, 182, 0.8);
                color: #2c3e50;
            }
        """)
        folder_label.setWordWrap(True)
        
        # تسمية حقل الرمز السري
        password_label = QLabel("🔐 أدخل الرمز السري (يجب أن يكون قوياً - 6 أحرف على الأقل):", self)
        password_label.setFont(QFont("Calibri", 13, QFont.Bold))
        password_label.setGeometry(40, 210, 570, 30)
        password_label.setStyleSheet("""
            color: white; 
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        """)
        
        # حقل إدخال الرمز السري
        self.password_input = QLineEdit(self)
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(QFont("Calibri", 13))
        self.password_input.setGeometry(40, 250, 570, 50)
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #9b59b6;
                border-radius: 10px;
                font-size: 16px;
                background-color: rgba(255, 255, 255, 0.95);
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #8e44ad;
                background-color: white;
            }
        """)
        self.password_input.setPlaceholderText("أدخل رمزاً سرياً قوياً...")
        
        # تسمية تأكيد الرمز السري
        confirm_label = QLabel("🔄 تأكيد الرمز السري:", self)
        confirm_label.setFont(QFont("Calibri", 13, QFont.Bold))
        confirm_label.setGeometry(40, 320, 570, 30)
        confirm_label.setStyleSheet("""
            color: white; 
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
        """)
        
        # حقل تأكيد الرمز السري
        self.confirm_password_input = QLineEdit(self)
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setFont(QFont("Calibri", 13))
        self.confirm_password_input.setGeometry(40, 360, 570, 50)
        self.confirm_password_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #9b59b6;
                border-radius: 10px;
                font-size: 16px;
                background-color: rgba(255, 255, 255, 0.95);
                color: #2c3e50;
            }
            QLineEdit:focus {
                border-color: #8e44ad;
                background-color: white;
            }
        """)
        self.confirm_password_input.setPlaceholderText("أعد إدخال الرمز السري للتأكيد...")
        
        # زر تطبيق الحماية
        apply_btn = QPushButton("🔒 تطبيق الحماية الآن", self)
        apply_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        apply_btn.setGeometry(250, 430, 200, 60)
        apply_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2ecc71, stop:1 #27ae60);
                color: white;
                border: 2px solid #27ae60;
                border-radius: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #27ae60, stop:1 #229954);
                border-color: #229954;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #229954, stop:1 #1e8449);
            }
        """)
        apply_btn.clicked.connect(self.apply_protection)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء", self)
        cancel_btn.setFont(QFont("Calibri", 14, QFont.Bold))
        cancel_btn.setGeometry(470, 430, 120, 60)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border: 2px solid #c0392b;
                border-radius: 12px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #a93226);
                border-color: #a93226;
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #a93226, stop:1 #922b21);
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
    def apply_protection(self):
        """تطبيق الحماية على المجلد"""
        password = self.password_input.text().strip()
        confirm_password = self.confirm_password_input.text().strip()
        
        # التحقق من صحة البيانات
        if not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رمز سري!")
            return
            
        if len(password) < 6:
            QMessageBox.warning(self, "خطأ", "الرمز السري يجب أن يكون 6 أحرف على الأقل!")
            return
            
        if password != confirm_password:
            QMessageBox.warning(self, "خطأ", "الرمز السري وتأكيده غير متطابقان!")
            return
        
        # تأكيد العملية
        reply = QMessageBox.question(
            self,
            "تأكيد الحماية",
            f"هل أنت متأكد من تطبيق الحماية على المجلد:\n{self.folder_path}\n\n"
            "⚠️ تحذير: بدون الرمز السري لن تتمكن من الوصول للمجلد!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # تطبيق الحماية (هنا يمكن إضافة الكود الفعلي للحماية)
                self.secure_folder_with_password(self.folder_path, password)
                
                QMessageBox.information(
                    self,
                    "نجح التطبيق",
                    f"تم تأمين المجلد بنجاح!\n\n"
                    f"المجلد المحمي: {self.folder_path}\n"
                    f"⚠️ احتفظ بالرمز السري في مكان آمن"
                )
                
                self.accept()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تطبيق الحماية:\n{str(e)}")
    
    def secure_folder_with_password(self, folder_path, password):
        """تطبيق حماية إغلاق المجلد برمز سري (بدون تشفير الملفات)"""
        try:
            # 1. تشفير الرمز السري
            hashed_password = hashlib.sha256(password.encode()).hexdigest()
            
            # 2. إنشاء ملف حماية مع معلومات الحماية
            protection_file = os.path.join(folder_path, ".folder_protection")
            protection_data = {
                'protected': 'true',
                'password_hash': hashed_password,
                'protection_date': datetime.datetime.now().isoformat(),
                'folder_name': os.path.basename(folder_path),
                'protection_type': 'folder_lock',
                'program_can_access': 'true'  # البرنامج يمكنه الوصول
            }
            
            with open(protection_file, 'w', encoding='utf-8') as f:
                for key, value in protection_data.items():
                    f.write(f"{key}={value}\n")
            
            # 3. إخفاء ملف الحماية
            if sys.platform == "win32":
                try:
                    subprocess.run(['attrib', '+h', '+s', protection_file], 
                                 capture_output=True, shell=True)
                except Exception as e:
                    print(f"تحذير: لم يتم إخفاء ملف الحماية: {e}")
            
            # 4. تطبيق حماية Windows على المجلد (اختياري)
            self.apply_windows_folder_protection(folder_path)
            
            # 5. إنشاء ملف تحذير مرئي
            warning_file = os.path.join(folder_path, "🔒 مجلد محمي - اقرأ التعليمات.txt")
            with open(warning_file, 'w', encoding='utf-8') as f:
                f.write("🔒 هذا المجلد محمي برمز سري\n")
                f.write("="*60 + "\n\n")
                f.write("⚠️ تحذير مهم:\n")
                f.write("هذا المجلد محمي من التعديل والوصول غير المصرح به.\n\n")
                f.write("📋 للوصول إلى محتويات المجلد:\n")
                f.write("1. افتح البرنامج (taheri22.exe أو app.py)\n")
                f.write("2. اذهب إلى إعدادات البرنامج\n")
                f.write("3. اختر 'فتح مجلد محمي'\n")
                f.write("4. أدخل الرمز السري الصحيح\n\n")
                f.write("🚫 محاولة تعديل أو حذف الملفات بدون إذن ممنوعة!\n\n")
                f.write("💡 البرنامج يعمل بشكل طبيعي حتى مع تطبيق الحماية.\n")
                f.write(f"📅 تاريخ الحماية: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 6. حفظ معلومات المجلد المحمي في قاعدة البيانات
            self.save_protected_folder_info(folder_path, hashed_password)
            
            # 7. إنشاء اختصار للبرنامج في المجلد المحمي
            self.create_program_shortcut_in_folder(folder_path)
                    
            print(f"تم تأمين المجلد بنجاح: {folder_path}")
            
        except Exception as e:
            raise Exception(f"فشل في تأمين المجلد: {str(e)}")
    
    def apply_windows_folder_protection(self, folder_path):
        """تطبيق حماية Windows فعالة على المجلد"""
        try:
            # 1. تطبيق حماية للقراءة فقط على جميع الملفات
            for root, dirs, files in os.walk(folder_path):
                # حماية المجلدات الفرعية
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        # إزالة صلاحيات الكتابة والحذف
                        import stat
                        os.chmod(dir_path, stat.S_IREAD | stat.S_IEXEC)
                    except:
                        pass
                
                # حماية الملفات
                for file in files:
                    file_path = os.path.join(root, file)
                    if not file.startswith('.folder_protection'):  # تجنب حماية ملف الحماية
                        try:
                            # تطبيق حماية القراءة فقط
                            import stat
                            os.chmod(file_path, stat.S_IREAD)
                        except:
                            pass
            
            # 2. إنشاء ملف Autorun.inf لمنع الوصول السهل
            autorun_file = os.path.join(folder_path, "autorun.inf")
            try:
                with open(autorun_file, 'w', encoding='utf-8') as f:
                    f.write("[autorun]\n")
                    f.write("open=🔒 مجلد محمي - اقرأ التعليمات.txt\n")
                    f.write("icon=🔒 مجلد محمي - اقرأ التعليمات.txt\n")
                    f.write("label=🔒 مجلد محمي برمز سري\n")
                
                # إخفاء ملف autorun
                try:
                    import subprocess
                    subprocess.run(['attrib', '+h', '+s', autorun_file], 
                                 capture_output=True, shell=True)
                except:
                    pass
            except:
                pass
            
            # 3. تطبيق حماية إضافية على المجلد الرئيسي
            try:
                import stat
                current_permissions = os.stat(folder_path).st_mode
                # منع الكتابة مع السماح بالقراءة والتنفيذ
                new_permissions = stat.S_IREAD | stat.S_IEXEC
                os.chmod(folder_path, new_permissions)
            except Exception as e:
                print(f"تحذير: فشل في تطبيق حماية المجلد الرئيسي: {e}")
                            
        except Exception as e:
            print(f"تحذير: فشل في تطبيق حماية Windows: {e}")
    
    def create_program_shortcut_in_folder(self, folder_path):
        """إنشاء اختصار للبرنامج داخل المجلد المحمي"""
        try:
            # إنشاء ملف batch لفتح البرنامج
            batch_file = os.path.join(folder_path, "🔓 فتح البرنامج.bat")
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            with open(batch_file, 'w', encoding='utf-8') as f:
                f.write("@echo off\n")
                f.write("title برنامج taheri22 - الوصول المحمي\n")
                f.write("echo ================================\n")
                f.write("echo    برنامج taheri22 المحمي\n") 
                f.write("echo ================================\n")
                f.write("echo.\n")
                f.write("echo تم فتح البرنامج من المجلد المحمي...\n")
                f.write("echo.\n")
                f.write(f"cd /d \"{current_dir}\"\n")
                
                # البحث عن الملف التنفيذي المناسب
                exe_files = ['taheri22.exe', 'app.exe', 'main.exe']
                py_files = ['app.py', 'main_window.py', 'taheri22.py']
                
                for exe_file in exe_files:
                    f.write(f"if exist \"{exe_file}\" (\n")
                    f.write(f"    start \"\" \"{exe_file}\"\n")
                    f.write(f"    goto :end\n")
                    f.write(f")\n")
                
                for py_file in py_files:
                    f.write(f"if exist \"{py_file}\" (\n")
                    f.write(f"    python \"{py_file}\"\n")
                    f.write(f"    goto :end\n")
                    f.write(f")\n")
                
                f.write("echo خطأ: لم يتم العثور على الملف التنفيذي للبرنامج!\n")
                f.write("pause\n")
                f.write(":end\n")
                f.write("exit\n")
                
        except Exception as e:
            print(f"تحذير: فشل في إنشاء اختصار البرنامج: {e}")
    
    def remove_windows_folder_protection(self, folder_path):
        """إزالة حماية Windows من المجلد بشكل شامل"""
        try:
            import stat
            
            # إزالة الحماية من المجلد الرئيسي أولاً
            try:
                # إعادة صلاحيات كاملة للمجلد
                os.chmod(folder_path, stat.S_IWRITE | stat.S_IREAD | stat.S_IEXEC)
            except Exception as e:
                print(f"تحذير: فشل في إزالة حماية المجلد الرئيسي: {e}")
            
            # إزالة الحماية من جميع الملفات والمجلدات الفرعية
            for root, dirs, files in os.walk(folder_path):
                # إزالة الحماية من المجلدات الفرعية
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    try:
                        os.chmod(dir_path, stat.S_IWRITE | stat.S_IREAD | stat.S_IEXEC)
                    except:
                        pass
                
                # إزالة الحماية من الملفات
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        os.chmod(file_path, stat.S_IWRITE | stat.S_IREAD | stat.S_IEXEC)
                    except:
                        pass
            
            # حذف ملف autorun.inf إذا كان موجوداً
            autorun_file = os.path.join(folder_path, "autorun.inf")
            if os.path.exists(autorun_file):
                try:
                    # إزالة الإخفاء أولاً
                    import subprocess
                    subprocess.run(['attrib', '-h', '-s', autorun_file], 
                                 capture_output=True, shell=True)
                    os.remove(autorun_file)
                except:
                    pass
                        
        except Exception as e:
            print(f"تحذير: فشل في إزالة حماية Windows: {e}")
    
    def check_folder_permissions(self, folder_path):
        """فحص صلاحيات الوصول للمجلد"""
        try:
            # محاولة إنشاء ملف تجريبي
            test_file = os.path.join(folder_path, f"test_permission_{int(time.time())}.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            return True
        except (PermissionError, OSError):
            return False
    
    def check_locked_files(self, folder_path):
        """فحص الملفات المقفلة في المجلد"""
        locked_files = []
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        # محاولة فتح الملف للكتابة (سيفشل إذا كان مقفلاً)
                        with open(file_path, 'r+b') as f:
                            pass
                    except (PermissionError, OSError):
                        locked_files.append(file_path)
                        if len(locked_files) >= 10:  # حد أقصى 10 ملفات للفحص
                            break
        except Exception:
            pass
        return locked_files
    
    def force_remove_directory(self, directory_path):
        """إزالة مجلد بقوة مع معالجة مشاكل الصلاحيات"""
        try:
            def handle_remove_readonly(func, path, exc):
                """معالج لحذف الملفات للقراءة فقط"""
                if os.path.exists(path):
                    os.chmod(path, 0o777)
                    func(path)
            
            shutil.rmtree(directory_path, onerror=handle_remove_readonly)
        except Exception as e:
            print(f"تحذير: فشل في حذف المجلد {directory_path}: {e}")
    
    def check_folder_safety(self, folder_path):
        """فحص أمان المجلد قبل الحماية"""
        try:
            # فحص ما إذا كان مجلد نظام
            system_folders = [
                'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
                'C:\\ProgramData', 'C:\\Users\\<USER>\\System Volume Information'
            ]
            
            folder_path_normalized = os.path.normpath(folder_path).upper()
            for sys_folder in system_folders:
                if folder_path_normalized.startswith(sys_folder.upper()):
                    return {
                        'safe': False, 
                        'reason': f'هذا مجلد نظام مهم ({sys_folder}).\nحماية مجلدات النظام قد تؤثر على استقرار الحاسوب.'
                    }
            
            # فحص الصلاحيات
            if not self.check_folder_permissions(folder_path):
                return {
                    'safe': False,
                    'reason': 'ليس لديك صلاحيات كافية للوصول إلى هذا المجلد.\nيرجى تشغيل البرنامج كمدير أو اختيار مجلد آخر.'
                }
            
            # فحص الملفات المقفلة
            locked_files = self.check_locked_files(folder_path)
            if len(locked_files) > 3:  # إذا كان هناك أكثر من 3 ملفات مقفلة
                return {
                    'safe': False,
                    'reason': f'يوجد {len(locked_files)} ملف مفتوح في برامج أخرى.\nيرجى إغلاق جميع البرامج التي تستخدم ملفات هذا المجلد أولاً.'
                }
            
            # فحص المساحة المتاحة
            try:
                folder_size = self.get_folder_size_bytes(folder_path)
                free_space = shutil.disk_usage(folder_path)[2]  # المساحة الفارغة
                if folder_size * 2 > free_space:  # نحتاج مساحة مضاعفة للنسخ الاحتياطي
                    return {
                        'safe': False,
                        'reason': 'المساحة المتاحة في القرص غير كافية.\nنحتاج مساحة إضافية تساوي ضعف حجم المجلد لإنشاء نسخة احتياطية آمنة.'
                    }
            except:
                pass  # تجاهل أخطاء فحص المساحة
            
            return {'safe': True, 'reason': ''}
            
        except Exception as e:
            return {
                'safe': False,
                'reason': f'حدث خطأ أثناء فحص المجلد:\n{str(e)}'
            }
    
    def get_folder_size_bytes(self, folder_path):
        """حساب حجم المجلد بالبايت"""
        total_size = 0
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                try:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)
                except:
                    continue
        return total_size
            
    def clear_folder_contents_safely(self, folder_path):
        """حذف محتويات المجلد بأمان (عدا ملفات الحماية)"""
        try:
            for item in os.listdir(folder_path):
                if item.startswith('.folder_protection'):
                    continue
                    
                item_path = os.path.join(folder_path, item)
                try:
                    if os.path.isdir(item_path):
                        self.force_remove_directory(item_path)
                    else:
                        # إزالة حماية القراءة فقط قبل الحذف
                        if os.path.exists(item_path):
                            os.chmod(item_path, 0o777)
                        os.remove(item_path)
                except PermissionError as e:
                    raise Exception(f"لا يمكن حذف الملف: {item}\nالخطأ: {str(e)}")
        except Exception as e:
            print(f"خطأ في حذف محتويات المجلد: {e}")
            raise
    
    def save_protected_folder_info(self, folder_path, password_hash):
        """حفظ معلومات المجلد المحمي في قاعدة البيانات"""
        try:
            conn = get_database_connection()
            cursor = conn.cursor()
            
            # إنشاء جدول للمجلدات المحمية إذا لم يكن موجوداً
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS protected_folders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    folder_path TEXT UNIQUE,
                    password_hash TEXT,
                    protection_date TEXT,
                    folder_name TEXT,
                    is_active INTEGER DEFAULT 1
                )
            """)
            
            # إدراج معلومات المجلد المحمي
            cursor.execute("""
                INSERT OR REPLACE INTO protected_folders 
                (folder_path, password_hash, protection_date, folder_name, is_active)
                VALUES (?, ?, ?, ?, 1)
            """, (
                folder_path,
                password_hash,
                datetime.datetime.now().isoformat(),
                os.path.basename(folder_path)
            ))
            
            conn.commit()
            conn.close()
            print("تم حفظ معلومات المجلد المحمي في قاعدة البيانات")
            
        except Exception as e:
            print(f"تحذير: لم يتم حفظ معلومات الحماية في قاعدة البيانات: {e}")
    
    def reject(self):
        """إلغاء العملية"""
        super().reject()

if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    window = Sub8Window()
    window.show()
    sys.exit(app.exec_())


