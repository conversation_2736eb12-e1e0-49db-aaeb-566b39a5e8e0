"""
اخ    print("🧪 اختبار تقرير الوافدين والمغادرين المُحدَث...")
    print("📄 تحسينات توفير المساحة:")
    print("   • إزالة عنوان 'لائحة التحويلات الوافدين'")
    print("   • إزالة الخط الفاصل تحت العنوان")
    print("   • هوامش صفحة مقللة إلى 0.2 سم من جميع الجهات")
    print("   • زيادة عرض عمود رقم التلميذ بـ 5 نقاط")
    print("   • زيادة عدد السجلات من 10 إلى 15 سجل في كل صفحة")
    print("🎨 تم تطبيق التنسيق الموحد مع arabic_pdf_report")
    print("🆕 التحسينات الجديدة:")
    print("   • إضافة عمود الترتيب")
    print("   • دمج عمودي الإسم والنسب في عمود واحد")
    print("   • خلفية زرقاء داكنة للعناوين (نفس التقرير الأصلي)")
    print("   • خط Calibri-Bold 14 للعناوين، 12 للبيانات")
    print("   • ألوان وتنسيق موحد مع النظام الأساسي")ر الوافدين والمغادرين - مع التحسينات الجديدة
"""

from arrivals_departures_report import create_arrivals_departures_report

if __name__ == "__main__":
    print("🧪 اختبار تقرير الوافدين والمغادرين المُحدَث...")
    print("🔧 تم إصلاح مشكلة تداخل الجدول مع الترويسة")
    print("� تم تطبيق التنسيق الموحد مع arabic_pdf_report")
    print("�🆕 التحسينات الجديدة:")
    print("   • إضافة عمود الترتيب")
    print("   • دمج عمودي الإسم والنسب في عمود واحد")
    print("   • خلفية زرقاء داكنة للعناوين (نفس التقرير الأصلي)")
    print("   • خط Calibri-Bold 14 للعناوين، 12 للبيانات")
    print("   • ألوان وتنسيق موحد مع النظام الأساسي")
    
    # إنشاء التقرير مع البيانات التجريبية وفتحه تلقائياً
    output_path = create_arrivals_departures_report(auto_open=True)
    
    if output_path:
        print(f"✅ تم إنشاء التقرير المُحدَث بنجاح: {output_path}")
        print("📋 المميزات المطبقة:")
        print("   • عمود الترتيب الجديد")
        print("   • عمود الاسم والنسب المدمج")
        print("   • عرض محسن لرقم التلميذ (+5 نقاط)")
        print("   • هوامش مختصرة (0.2 سم) لتوفير مساحة أكبر")
        print("   • إزالة العناوين الفرعية والخطوط الفاصلة")
        print("   • 15 سجل في كل صفحة (زيادة من 10)")
        print("   • تنسيق موحد مع arabic_pdf_report:")
        print("     - عناوين: خلفية زرقاء داكنة مع نص أبيض")
        print("     - بيانات: خلفية بيضاء/رمادية مع نص أسود")
        print("     - خط Calibri-Bold للوضوح والاحترافية")
        print("   • تحسين المساحات والهوامش")
        print("   • ضبط أحجام الخطوط")
        print("   • موضع ثابت آمن للجدول")
    else:
        print("❌ فشل في إنشاء التقرير")