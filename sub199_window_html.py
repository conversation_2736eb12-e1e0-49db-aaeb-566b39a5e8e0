import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QMessageBox, QDialog, 
                            QInputDialog, QLineEdit)
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt, QUrl, pyqtSignal, QDate, QObject, pyqtSlot
from PyQt5.QtSql import QSqlQuery
import sqlite3
import json
from urllib.parse import unquote
from database_config import get_database_path, get_database_connection

# تم إزالة استيراد sub100_window والاعتماد فقط على نظام الرسائل عبر الجسر

# استيراد دالة الطباعة المتطورة للوافدين والمغادرين
try:
    from arrivals_departures_report import create_arrivals_departures_report as arrivals_print_function
    ARRIVALS_PDF_AVAILABLE = True
    print("تم استيراد دالة الطباعة من arrivals_departures_report.py بنجاح")
except ImportError:
    ARRIVALS_PDF_AVAILABLE = False
    print("تعذر استيراد arrivals_departures_report.py")

class WebBridge(QObject):
    """فئة الجسر بين JavaScript و Python"""
    
    # إشارات للتفاعل مع النافذة الرئيسية
    dataRequested = pyqtSignal()
    deleteCertificates = pyqtSignal(list)
    printCertificates = pyqtSignal()
    updateTable = pyqtSignal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_path = None
        self.using_qsql = False
        self.db = None
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        
    @pyqtSlot(str)
    def deleteSelectedCertificates(self, selected_codes):
        """استقبال قائمة الرموز المحددة لحذف السجلات"""
        try:
            codes_list = json.loads(selected_codes)
            if not codes_list:
                self.showMessage("الرجاء تحديد سجل واحد على الأقل لحذفه", "warning")
                return
            self.deleteCertificates.emit(codes_list)
        except Exception as e:
            print(f"خطأ في معالجة الرموز المحددة للحذف: {e}")
            self.showMessage("حدث خطأ في معالجة البيانات المحددة", "error")
    
    @pyqtSlot()
    def requestPrint(self):
        """طلب طباعة الشهادات"""
        try:
            self.printCertificates.emit()
        except Exception as e:
            print(f"خطأ في طلب الطباعة: {e}")
            self.showMessage("حدث خطأ أثناء محاولة الطباعة", "error")
    
    @pyqtSlot(str, str)
    def filterData(self, notes_filter, year_filter):
        """تصفية السجلات حسب الملاحظات والسنة الدراسية"""
        try:
            print(f"طلب تصفية - نوع: '{notes_filter}' - سنة: '{year_filter}'")
            
            # تحديد العمود المطلوب حسب نوع التصفية
            if notes_filter == 'لائحة التحويلات (المغادرون)':
                institution_column = 'مؤسسة_الإستقبال'
            else:
                institution_column = 'المؤسسة_الأصلية'
            
            # بناء الاستعلام حسب المعاملات
            query_str = f"""
                SELECT رقم_التلميذ, النسب, الإسم, المستوى, تاريخ_التحويل,
                       {institution_column}, المديرية_الإقليمية_الأصلية, ملاحظات, id
                FROM سجلات_الوافدين_والمغادرين
                WHERE ملاحظات = '{notes_filter}'
            """
            
            # إضافة شرط السنة (الآن مطلوب دائماً)
            if year_filter:
                query_str += f" AND السنة_الدراسية = '{year_filter}'"
            
            query_str += " ORDER BY id DESC"
            
            print(f"الاستعلام المستخدم: {query_str}")
            
            if self.using_qsql:
                query = QSqlQuery(self.db)
                if not query.exec_(query_str):
                    raise Exception(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")

                records = []
                while query.next():
                    record = []
                    for i in range(9):  # 9 أعمدة
                        value = query.value(i)
                        record.append(str(value) if value is not None else '')
                    records.append(record)
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query_str)
                records = cursor.fetchall()
                conn.close()
                
            print(f"عدد السجلات المسترجعة: {len(records)}")
            
            # تحديث الجدول مع تمرير نوع التصفية
            self.updateTable.emit([records, notes_filter])
            
        except Exception as e:
            print(f"خطأ في تصفية البيانات: {e}")
            self.showMessage("حدث خطأ أثناء تصفية البيانات", "error")
    
    @pyqtSlot()
    def populateYearFilter(self):
        """تعبئة قائمة تصفية السنوات الدراسية"""
        try:
            query_str = """
                SELECT DISTINCT السنة_الدراسية
                FROM سجلات_الوافدين_والمغادرين
                WHERE السنة_الدراسية IS NOT NULL AND السنة_الدراسية != ''
                ORDER BY السنة_الدراسية
            """
            
            years = []
            if self.using_qsql:
                query = QSqlQuery(self.db)
                if query.exec_(query_str):
                    while query.next():
                        year = query.value(0)
                        if year:
                            years.append(str(year))
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query_str)
                results = cursor.fetchall()
                years = [str(row[0]) for row in results if row[0]]
                conn.close()
            
            print(f"السنوات الدراسية المتاحة: {years}")
            
            # إرسال البيانات إلى JavaScript
            if self.parent_window and hasattr(self.parent_window, 'web_view'):
                years_data = json.dumps(years, ensure_ascii=False)
                self.parent_window.web_view.page().runJavaScript(f"populateYearFilter({years_data});")
                
        except Exception as e:
            print(f"خطأ في جلب السنوات الدراسية: {e}")
            self.showMessage("حدث خطأ أثناء جلب السنوات الدراسية", "error")
    
    def get_first_year(self):
        """الحصول على السنة الدراسية الأولى"""
        try:
            query_str = """
                SELECT DISTINCT السنة_الدراسية
                FROM سجلات_الوافدين_والمغادرين
                WHERE السنة_الدراسية IS NOT NULL AND السنة_الدراسية != ''
                ORDER BY السنة_الدراسية
                LIMIT 1
            """
            
            if self.using_qsql:
                query = QSqlQuery(self.db)
                if query.exec_(query_str) and query.next():
                    return str(query.value(0))
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query_str)
                result = cursor.fetchone()
                conn.close()
                if result:
                    return str(result[0])
            return None
                
        except Exception as e:
            print(f"خطأ في جلب السنة الأولى: {e}")
            return None
    
    @pyqtSlot(str)
    def copyCodeToClipboard(self, code):
        """نسخ الرمز إلى الحافظة"""
        try:
            from PyQt5.QtGui import QClipboard
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(code)
            self.showMessage(f"تم نسخ الرمز: {code}", "success")
        except Exception as e:
            print(f"خطأ في نسخ الرمز: {e}")
            self.showMessage("حدث خطأ أثناء نسخ الرمز", "error")
    
    @pyqtSlot(str, str)  
    def deleteAllFilteredRecords(self, notes_filter, year_filter):
        """حذف جميع السجلات حسب التصفية المحددة"""
        try:
            print(f"طلب حذف جميع السجلات - نوع: '{notes_filter}' - سنة: '{year_filter}'")
            
            # بناء الاستعلام للحصول على عدد السجلات أولاً
            count_query = """
                SELECT COUNT(*) FROM سجلات_الوافدين_والمغادرين
                WHERE ملاحظات = ?
            """
            params = [notes_filter]
            
            if year_filter:
                count_query += " AND السنة_الدراسية = ?"
                params.append(year_filter)
                
            # الحصول على عدد السجلات
            if self.using_qsql:
                query = QSqlQuery(self.db)
                query.prepare(count_query)
                for param in params:
                    query.addBindValue(param)
                    
                if query.exec_() and query.next():
                    total_records = query.value(0)
                else:
                    raise Exception(f"خطأ في عد السجلات: {query.lastError().text()}")
            else:
                import sqlite3
                conn = sqlite3.connect(get_database_path())
                cursor = conn.cursor()
                cursor.execute(count_query, params)
                result = cursor.fetchone()
                total_records = result[0] if result else 0
                conn.close()
            
            if total_records == 0:
                self.showMessage("لا توجد سجلات للحذف حسب التصفية المحددة", "warning")
                return
                
            # بناء استعلام الحذف
            delete_query = """
                DELETE FROM سجلات_الوافدين_والمغادرين
                WHERE ملاحظات = ?
            """
            if year_filter:
                delete_query += " AND السنة_الدراسية = ?"
                
            # تنفيذ الحذف
            deleted_count = 0
            if self.using_qsql:
                query = QSqlQuery(self.db)
                query.prepare(delete_query)
                for param in params:
                    query.addBindValue(param)
                    
                if query.exec_():
                    deleted_count = query.numRowsAffected()
                else:
                    raise Exception(f"خطأ في حذف السجلات: {query.lastError().text()}")
            else:
                import sqlite3
                conn = sqlite3.connect(get_database_path())
                cursor = conn.cursor()
                cursor.execute(delete_query, params)
                deleted_count = cursor.rowcount
                conn.commit()
                conn.close()
                
            # عرض رسالة النجاح
            success_msg = f"✅ تم حذف {deleted_count} سجل بنجاح من إجمالي {total_records} سجل"
            filter_details = f"\nالتصفية: {notes_filter}"
            if year_filter:
                filter_details += f" - السنة: {year_filter}"
                
            self.showMessage(success_msg + filter_details, "success")
            
            # إعادة تحميل البيانات
            self.filterData(notes_filter, year_filter)
            
        except Exception as e:
            print(f"خطأ في حذف جميع السجلات: {e}")
            self.showMessage(f"حدث خطأ أثناء حذف السجلات: {str(e)}", "error")

    def showMessage(self, message, message_type="info"):
        """عرض رسالة للمستخدم عبر JavaScript فقط"""
        try:
            # إرسال الرسالة لـ JavaScript للعرض في الواجهة
            message_data = json.dumps({"message": message, "type": message_type}, ensure_ascii=False)
            if self.parent_window and hasattr(self.parent_window, 'web_view'):
                self.parent_window.web_view.page().runJavaScript(f"showNotification({message_data});")
        except Exception as e:
            print(f"خطأ في عرض الرسالة: {e}")

class SchoolCertificateHtmlWindow(QMainWindow):
    """نافذة إدارة طلبات الشهادات المدرسية باستخدام HTML"""
    
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.db_path = get_database_path()
        self.using_qsql = bool(db and hasattr(db, 'isOpen') and db.isOpen())
        
        self.setWindowTitle("إدارة سجلات الوافدين والمغادرين")
        self.setMinimumSize(1200, 700)
        
        # إعداد أيقونة النافذة
        try:
            icon_path = os.path.join(os.path.dirname(__file__), "01.ico")
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except:
            pass
        
        self.setup_ui()
        self.setup_bridge()
        self.load_html_content()
        
        # إعداد خاص عند الدمج في النافذة الرئيسية
        if parent is not None:
            self.setup_for_embedding()
        
    def setup_for_embedding(self):
        """إعدادات خاصة عند دمج النافذة في النافذة الرئيسية"""
        try:
            # ضمان ظهور أسهم التمرير
            from PyQt5.QtWebEngineWidgets import QWebEngineSettings
            settings = self.web_view.settings()
            settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
            settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
            settings.setAttribute(QWebEngineSettings.SpatialNavigationEnabled, True)
            
            # إعدادات إضافية للنافذة
            self.setMinimumSize(1000, 700)
            
            print("INFO: تم تطبيق إعدادات الدمج لنافذة طلبات الشهادات المدرسية")
            
        except Exception as e:
            print(f"خطأ في إعداد النافذة للدمج: {e}")
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء عارض الويب
        self.web_view = QWebEngineView()
        
        # إعدادات خاصة للتمرير عند الدمج
        from PyQt5.QtWebEngineWidgets import QWebEngineSettings
        settings = self.web_view.settings()
        settings.setAttribute(QWebEngineSettings.ShowScrollBars, True)
        settings.setAttribute(QWebEngineSettings.ScrollAnimatorEnabled, True)
        
        # إعدادات إضافية لضمان عمل التمرير
        self.web_view.setMinimumSize(800, 600)
        
        layout.addWidget(self.web_view)
        
    def setup_bridge(self):
        """إعداد الجسر بين Python و JavaScript"""
        self.bridge = WebBridge(self)
        
        # تمرير معلومات قاعدة البيانات إلى الجسر
        self.bridge.db_path = self.db_path
        self.bridge.using_qsql = self.using_qsql
        self.bridge.db = self.db
        
        # ربط الإشارات
        self.bridge.dataRequested.connect(self.load_data)
        self.bridge.deleteCertificates.connect(self.delete_certificates)
        self.bridge.printCertificates.connect(self.preview_and_print)
        self.bridge.updateTable.connect(self.update_table_data)
        
    def load_html_content(self):
        """تحميل محتوى HTML"""
        html_content = self.generate_html()
        self.web_view.setHtml(html_content)
        
        # تحميل البيانات بعد تحميل HTML
        self.web_view.loadFinished.connect(self.on_load_finished)
        
    def on_load_finished(self):
        """يتم استدعاؤها عند انتهاء تحميل الصفحة"""
        # إعداد القناة للتواصل مع JavaScript
        from PyQt5.QtWebChannel import QWebChannel
        
        self.channel = QWebChannel()
        self.channel.registerObject("bridge", self.bridge)
        self.web_view.page().setWebChannel(self.channel)
        
        # تأخير قصير للتأكد من تحميل QWebChannel
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(500, self.load_data)
        
    def update_table_data(self, data):
        """تحديث بيانات الجدول بعد التصفية"""
        try:
            import json
            
            # فك تشكيل البيانات
            if isinstance(data, list) and len(data) == 2:
                records, filter_type = data
            else:
                records = data
                filter_type = "لائحة التحويلات (الوافدون)"  # افتراضي
            
            # تحويل البيانات إلى تنسيق JavaScript باستخدام JSON
            js_data = json.dumps(records, ensure_ascii=False)
            js_code = f"updateTable({js_data}, '{filter_type}');"
            
            # طباعة للتشخيص
            print(f"عدد السجلات المرسلة للجدول: {len(records)} - نوع التصفية: {filter_type}")
            
            # تنفيذ الكود في المتصفح
            self.web_view.page().runJavaScript(js_code)
            
        except Exception as e:
            print(f"خطأ في تحديث الجدول: {e}")
    
    def generate_html(self):
        """إنشاء محتوى HTML للنافذة"""
        return '''
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات الشهادات المدرسية</title>
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Calibri', 'Tahoma', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
            /* إعدادات التمرير للجسم */
            overflow: auto;
            scrollbar-width: auto;
            -ms-overflow-style: auto;
        }
        
        /* تخصيص أسهم التمرير للجسم */
        body::-webkit-scrollbar {
            width: 14px;
        }
        
        body::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 7px;
        }
        
        body::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
            backdrop-filter: blur(10px);
            /* إعدادات التمرير للحاوية */
            overflow: visible;
            min-height: 600px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }
        
        .header h1 {
            color: #1e3a8a;
            font-family: 'Calibri', sans-serif;
            font-size: 22pt;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: nowrap;
            justify-content: center;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(255, 255, 255, 0.95);
            padding: 10px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-family: 'Calibri', sans-serif;
            font-size: 14pt;
            font-weight: bold;
            cursor: pointer;
            transition: none;
            min-width: 200px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            white-space: nowrap;
        }
        
        .btn:hover {
            /* إزالة تأثيرات الحركة للجعل الأزرار ثابتة */
            transform: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn-purple {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
            padding: 15px 10px;
            text-align: center;
            font-weight: bold;
            font-size: 17px;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .table td {
            padding: 12px 10px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            font-size: 16px;
            font-weight: bold;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
            transform: scale(1.2);
        }
        
        .code-cell {
            color: #3498db;
            font-weight: bold;
            cursor: pointer;
        }
        
        .code-cell:hover {
            color: #2980b9;
            text-decoration: underline;
        }
        
        .delivered {
            background-color: #d5f4e6 !important;
        }
        
        .status-delivered {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-pending {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 18px;
            color: #7f8c8d;
        }
        
        .no-data {
            text-align: center;
            padding: 50px;
            font-size: 16px;
            color: #95a5a6;
        }
        
        .selected-count {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            margin-right: 15px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        /* أنماط عنصر التصفية */
        .filter-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-container select {
            transition: all 0.3s ease;
            font-family: 'Calibri', sans-serif;
            font-size: 16px;
            font-weight: bold;
            color: #000000;
        }
        
        .filter-container select:hover {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        
        .filter-container select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 10px rgba(0,123,255,0.5);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .controls {
                flex-direction: column;
            }
            
            .btn {
                min-width: 100%;
            }
            
            .table th,
            .table td {
                padding: 8px 5px;
                font-size: 12px;
            }
        }
        
        /* تحسينات إضافية للجدول */
        .table-wrapper {
            max-height: 60vh;
            overflow-y: auto;
            overflow-x: auto;
            border-radius: 10px;
            /* إجبار ظهور أسهم التمرير */
            scrollbar-width: auto; /* Firefox */
            -ms-overflow-style: auto; /* IE and Edge */
        }
        
        .table-wrapper::-webkit-scrollbar {
            width: 14px;
            height: 14px;
            background: #f8f9fa;
        }
        
        .table-wrapper::-webkit-scrollbar-track {
            background: #e9ecef;
            border-radius: 7px;
            border: 1px solid #dee2e6;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb {
            background: #6c757d;
            border-radius: 7px;
            border: 1px solid #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-thumb:hover {
            background: #495057;
        }
        
        .table-wrapper::-webkit-scrollbar-corner {
            background: #e9ecef;
        }
        
        /* إجبار ظهور التمرير دائماً */
        .table-wrapper {
            scrollbar-width: thin; /* Firefox */
            scrollbar-color: #6c757d #e9ecef; /* Firefox */
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎓 إدارة سجلات الوافدين والمغادرين</h1>
            <p>نظام متطور لإدارة وتتبع سجلات الوافدين والمغادرين</p>
        </div>
        
        <div class="filter-container" style="margin-bottom: 25px; background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 20px; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
            <div class="filter-grid" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 25px; align-items: end; max-width: 1000px; margin: 0 auto;">
                
                <div class="filter-item" style="display: flex; flex-direction: column; gap: 8px;">
                    <label for="notesFilter" style="font-weight: bold; color: #2c3e50; font-size: 16px; font-family: Calibri;">
                        📊 التصفية حسب حركية التلاميذ
                    </label>
                    <select id="notesFilter" 
                            style="padding: 12px 16px; 
                                   border: 2px solid #bdc3c7; 
                                   border-radius: 8px; 
                                   background: white; 
                                   font-family: Calibri; 
                                   font-size: 16px; 
                                   font-weight: bold; 
                                   color: #2c3e50;
                                   transition: all 0.3s ease;
                                   cursor: pointer;
                                   height: 48px;" 
                            onchange="filterData()"
                            onfocus="this.style.borderColor='#3498db'; this.style.boxShadow='0 0 8px rgba(52,152,219,0.3)'"
                            onblur="this.style.borderColor='#bdc3c7'; this.style.boxShadow='none'">
                        <option value="لائحة التحويلات (الوافدون)">🏫 لائحة التحويلات (الوافدون)</option>
                        <option value="لائحة التحويلات (المغادرون)">🚪 لائحة التحويلات (المغادرون)</option>
                    </select>
                </div>
                
                <div class="filter-item" style="display: flex; flex-direction: column; gap: 8px;">
                    <label for="yearFilter" style="font-weight: bold; color: #2c3e50; font-size: 16px; font-family: Calibri;">
                        📅 تصفية حسب السنة الدراسية
                    </label>
                    <select id="yearFilter" 
                            style="padding: 12px 16px; 
                                   border: 2px solid #bdc3c7; 
                                   border-radius: 8px; 
                                   background: white; 
                                   font-family: Calibri; 
                                   font-size: 16px; 
                                   font-weight: bold; 
                                   color: #2c3e50;
                                   transition: all 0.3s ease;
                                   cursor: pointer;
                                   height: 48px;" 
                            onchange="filterData()"
                            onfocus="this.style.borderColor='#e67e22'; this.style.boxShadow='0 0 8px rgba(230,126,34,0.3)'"
                            onblur="this.style.borderColor='#bdc3c7'; this.style.boxShadow='none'">
                    </select>
                </div>
                
                <div class="filter-item" style="display: flex; flex-direction: column; gap: 8px;">
                    <label style="font-weight: bold; color: #2c3e50; font-size: 16px; font-family: Calibri;">
                        📈 عدد السجلات
                    </label>
                    <div class="records-counter" 
                         style="background: linear-gradient(135deg, #27ae60, #2ecc71); 
                                color: white; 
                                padding: 12px 16px; 
                                border-radius: 8px; 
                                font-weight: bold; 
                                font-size: 16px; 
                                text-align: center;
                                box-shadow: 0 4px 15px rgba(39,174,96,0.3);
                                font-family: Calibri;
                                height: 48px;
                                display: flex;
                                align-items: center;
                                justify-content: center;">
                        <div id="recordsCount" style="display: flex; align-items: center; justify-content: center; gap: 8px;">
                            <span>0</span>
                        </div>
                    </div>
                </div>
                
            </div>
            
            <!-- استجابة للشاشات الصغيرة -->
            <style>
                @media (max-width: 768px) {
                    .filter-grid {
                        grid-template-columns: 1fr !important;
                        gap: 15px !important;
                        text-align: center;
                    }
                    
                    .filter-item select,
                    .filter-item .records-counter {
                        width: 100% !important;
                        max-width: 300px;
                        margin: 0 auto;
                    }
                }
            </style>
        </div>
        
        <div class="controls">
            
            <button id="deleteBtn" class="btn btn-danger" onclick="deleteCertificates()">
                حذف جميع السجلات
            </button>
            
            <button id="printBtn" class="btn btn-primary" onclick="printCertificates()">
                معاينة وطباعة التقرير
            </button>
            
        </div>
        
        <div class="table-container">
            <div class="table-wrapper">
                <table class="table" id="certificatesTable">
                    <thead>
                        <tr>
                            <th style="width: 100px;">رقم التلميذ</th>
                            <th style="width: 120px;">النسب</th>
                            <th style="width: 120px;">الإسم</th>
                            <th style="width: 150px;">المستوى</th>
                            <th style="width: 120px;">تاريخ التحويل</th>
                            <th style="width: 200px;">المؤسسة الأصلية</th>
                            <th style="width: 180px;">المديرية الإقليمية الأصلية</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr>
                            <td colspan="7" class="loading">جاري تحميل البيانات...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let bridge;
        let certificatesData = [];
        
        // إعداد القناة مع Python مع معالجة للأخطاء
        function setupBridge() {
            try {
                if (typeof QWebChannel !== 'undefined') {
                    // التحقق من وجود qt.webChannelTransport
                    if (typeof qt !== 'undefined' && qt.webChannelTransport) {
                        new QWebChannel(qt.webChannelTransport, function(channel) {
                            if (channel && channel.objects && channel.objects.bridge) {
                                bridge = channel.objects.bridge;
                                console.log('✅ تم إنشاء الجسر بنجاح');
                                
                                // اختبار الجسر
                                if (typeof bridge.populateYearFilter === 'function') {
                                    console.log('✅ وظائف الجسر متوفرة');
                                } else {
                                    console.log('⚠️ وظائف الجسر غير متوفرة بعد');
                                }
                            } else {
                                console.log('❌ فشل في إنشاء الجسر - channel غير صحيح');
                                setTimeout(setupBridge, 500);
                            }
                        });
                    } else {
                        console.log('⏳ qt.webChannelTransport غير متوفر، إعادة المحاولة...');
                        setTimeout(setupBridge, 200);
                    }
                } else {
                    console.log('⏳ QWebChannel غير متوفر، إعادة المحاولة...');
                    setTimeout(setupBridge, 200);
                }
            } catch (error) {
                console.log('❌ خطأ في إعداد الجسر:', error);
                setTimeout(setupBridge, 500);
            }
        }
        
        // بدء إعداد الجسر عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setupBridge();
        });
        
        // محاولة إضافية بعد تحميل النافذة
        window.addEventListener('load', function() {
            if (!bridge) {
                setTimeout(setupBridge, 300);
            }
        });
        
        function updateTable(data, filterType) {
            console.log("استلام بيانات الجدول:", data);
            console.log("عدد السجلات المستلمة:", data ? data.length : 0);
            console.log("نوع التصفية:", filterType);
            
            certificatesData = data;
            const tableBody = document.getElementById('tableBody');
            
            // تحديث رأس العمود حسب نوع التصفية
            updateColumnHeaders(filterType);
            
            // تحديث عدد السجلات
            updateRecordsCount(data ? data.length : 0);
            
            if (!data || data.length === 0) {
                console.log("لا توجد بيانات للعرض");
                tableBody.innerHTML = '<tr><td colspan="7" class="no-data">لا توجد بيانات للعرض</td></tr>';
                return;
            }
            
            console.log("سيتم عرض البيانات");
            let html = '';
            data.forEach((row, index) => {
                html += `
                    <tr>
                        <td class="code-cell" onclick="copyCode('${row[0]}')">${row[0] || ''}</td>
                        <td>${row[1] || ''}</td>
                        <td>${row[2] || ''}</td>
                        <td style="font-weight: bold;">${row[3] || ''}</td>
                        <td>${row[4] || ''}</td>
                        <td style="text-align: right;">${row[5] || ''}</td>
                        <td style="text-align: right;">${row[6] || ''}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
            
            // إعادة تطبيق إعدادات التمرير بعد تحديث البيانات
            setTimeout(function() {
                const wrapper = document.querySelector('.table-wrapper');
                if (wrapper) {
                    wrapper.style.overflowY = 'auto';
                    wrapper.style.overflowX = 'auto';
                    wrapper.style.scrollbarWidth = 'auto';
                    wrapper.style.msOverflowStyle = 'auto';
                }
            }, 100);
        }
        
        function updateColumnHeaders(filterType) {
            // تحديث رأس العمود السادس حسب نوع التصفية
            const headers = document.querySelectorAll('thead th');
            if (headers.length >= 6) {
                if (filterType === 'لائحة التحويلات (المغادرون)') {
                    headers[5].textContent = 'مؤسسة الاستقبال';
                } else {
                    headers[5].textContent = 'المؤسسة الأصلية';
                }
            }
        }
        
        function copyCode(code) {
            if (bridge && typeof bridge.copyCodeToClipboard === 'function') {
                bridge.copyCodeToClipboard(code);
            } else {
                // Fallback للنسخ المباشر إذا كان الجسر غير متاح
                navigator.clipboard.writeText(code).then(() => {
                    console.log('تم نسخ الرمز:', code);
                }).catch(() => {
                    console.log('فشل في نسخ الرمز:', code);
                });
            }
        }
        
        function deleteCertificates() {
            // طلب رمز التأكيد من المستخدم
            const confirmationCode = prompt('تحذير مهم!\\n\\nستقوم بحذف جميع السجلات حسب التصفية الحالية\\n\\nهذه العملية لا يمكن التراجع عنها\\n\\nلتأكيد العملية، الرجاء إدخال رمز التأكيد:');
            
            if (confirmationCode === null) {
                // المستخدم ضغط إلغاء
                showNotification({message: '✅ تم إلغاء عملية الحذف بأمان', type: 'info'});
                return;
            }
            
            if (confirmationCode !== '12345') {
                // رمز التأكيد خاطئ
                showNotification({message: '❌ رمز التأكيد غير صحيح - تم إلغاء عملية الحذف', type: 'error'});
                return;
            }
            
            // الحصول على معطيات التصفية الحالية
            const notesFilter = document.getElementById('notesFilter').value;
            const yearFilter = document.getElementById('yearFilter').value;
            
            // تأكيد إضافي
            const recordsCountElement = document.getElementById('recordsCount');
            const recordsCount = recordsCountElement ? recordsCountElement.textContent.replace(/[^0-9]/g, '') : '0';
            const finalConfirm = confirm(`تأكيد نهائي للحذف\\n\\nعدد السجلات المراد حذفها: ${recordsCount} سجل\\n\\nالتصفية المحددة:\\n• النوع: ${notesFilter}\\n• السنة الدراسية: ${yearFilter || 'جميع السنوات'}\\n\\nتنبيه: هذه العملية لا يمكن التراجع عنها!\\n\\nهل تريد المتابعة؟`);
            
            if (!finalConfirm) {
                showNotification({message: '🔒 تم إلغاء العملية - البيانات محفوظة بأمان', type: 'info'});
                return;
            }
            
            // إرسال طلب الحذف إلى الخادم
            if (bridge && typeof bridge.deleteAllFilteredRecords === 'function') {
                console.log('إرسال طلب حذف جميع السجلات للخادم');
                bridge.deleteAllFilteredRecords(notesFilter, yearFilter);
            } else {
                console.log('دالة حذف جميع السجلات غير متاحة');
                showNotification({message: 'دالة حذف جميع السجلات غير متاحة', type: 'error'});
            }
        }
        
        function printCertificates() {
            if (bridge && typeof bridge.requestPrint === 'function') {
                bridge.requestPrint();
            } else {
                console.log('الجسر غير متاح أو الدالة غير موجودة');
                showNotification({message: 'الجسر غير متاح، الرجاء المحاولة مرة أخرى', type: 'error'});
            }
        }
        
        function filterData() {
            const notesFilter = document.getElementById('notesFilter').value;
            const yearFilter = document.getElementById('yearFilter').value;
            document.getElementById('tableBody').innerHTML = 
                '<tr><td colspan="7" class="loading">جاري تصفية البيانات...</td></tr>';
            
            if (bridge && typeof bridge.filterData === 'function') {
                bridge.filterData(notesFilter, yearFilter);
            } else {
                console.log('دالة التصفية غير متاحة');
                showNotification({message: 'دالة التصفية غير متاحة', type: 'error'});
            }
        }
        
        function updateRecordsCount(count) {
            const countElement = document.getElementById('recordsCount');
            if (countElement) {
                countElement.innerHTML = '<span>' + count + '</span>';
            }
        }
        
        function populateYearFilter(years) {
            const yearSelect = document.getElementById('yearFilter');
            
            // مسح جميع الخيارات الموجودة
            yearSelect.innerHTML = '';
            
            // إضافة السنوات مع ترتيب وتنسيق جميل
            years.sort().forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year;
                yearSelect.appendChild(option);
            });
            
            console.log(`تم تحميل ${years.length} سنة دراسية في قائمة التصفية`);
        }
        
        // دالة لعرض الإشعارات الداخلية بدلاً من alert
        function showNotification(notificationData) {
            try {
                // إنشاء عنصر الإشعار
                const notification = document.createElement('div');
                notification.className = `notification notification-${notificationData.type || 'info'}`;
                notification.innerHTML = `
                    <div class="notification-content">
                        <span class="notification-message">${notificationData.message}</span>
                        <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
                    </div>
                `;
                
                // إضافة الأنماط إذا لم تكن موجودة
                if (!document.getElementById('notification-styles')) {
                    const styles = document.createElement('style');
                    styles.id = 'notification-styles';
                    styles.textContent = `
                        .notification {
                            position: fixed;
                            top: 20px;
                            right: 20px;
                            z-index: 10000;
                            padding: 15px 20px;
                            border-radius: 8px;
                            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            max-width: 400px;
                            direction: rtl;
                            text-align: right;
                            animation: slideIn 0.3s ease-out;
                        }
                        .notification-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
                        .notification-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
                        .notification-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
                        .notification-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
                        .notification-content { display: flex; align-items: center; justify-content: space-between; }
                        .notification-message { flex: 1; margin-right: 10px; }
                        .notification-close {
                            background: none; border: none; font-size: 18px; cursor: pointer;
                            color: inherit; opacity: 0.7; padding: 0; width: 20px; height: 20px;
                        }
                        .notification-close:hover { opacity: 1; }
                        @keyframes slideIn {
                            from { transform: translateX(100%); opacity: 0; }
                            to { transform: translateX(0); opacity: 1; }
                        }
                    `;
                    document.head.appendChild(styles);
                }
                
                // إضافة الإشعار للصفحة
                document.body.appendChild(notification);
                
                // إزالة الإشعار تلقائياً بعد 5 ثوانٍ
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 5000);
                
            } catch (error) {
                console.error('خطأ في عرض الإشعار:', error);
                // fallback لـ console في حالة فشل عرض الإشعار
                console.log(`إشعار [${notificationData.type}]: ${notificationData.message}`);
            }
        }
        
        // دالة لإعادة تطبيق إعدادات التمرير
        function reapplyScrollbarSettings() {
            try {
                const tableWrapper = document.querySelector('.table-wrapper');
                if (tableWrapper) {
                    tableWrapper.style.overflowY = 'auto';
                    tableWrapper.style.overflowX = 'auto';
                    tableWrapper.style.scrollbarWidth = 'auto';
                    tableWrapper.style.msOverflowStyle = 'auto';
                }
                
                // للجسم أيضاً
                document.body.style.overflow = 'auto';
                document.documentElement.style.overflow = 'auto';
                
                console.log('تم إعادة تطبيق إعدادات التمرير لنافذة الشهادات');
            } catch (error) {
                console.error('خطأ في إعادة تطبيق إعدادات التمرير:', error);
            }
        }
        
        // تطبيق إعدادات التمرير عند تحميل النافذة
        window.addEventListener('load', function() {
            setTimeout(reapplyScrollbarSettings, 500);
            setTimeout(reapplyScrollbarSettings, 1500);
        });
    </script>
</body>
</html>
        '''
        
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات وإرسالها إلى HTML"""
        try:
            # تعبئة قائمة السنوات الدراسية أولاً
            self.bridge.populateYearFilter()
            
            # الحصول على السنة الأولى لعرضها افتراضياً
            first_year = self.bridge.get_first_year()
            if not first_year:
                # إذا لم توجد سنوات، عرض جدول فارغ
                self.web_view.page().runJavaScript("updateTable([], 'لائحة التحويلات (الوافدون)');")
                return
            
            # عرض سجلات الوافدين للسنة الأولى افتراضياً
            default_filter = "لائحة التحويلات (الوافدون)"
            
            # تحديد العمود المطلوب حسب نوع التصفية
            if default_filter == 'لائحة التحويلات (المغادرون)':
                institution_column = 'مؤسسة_الإستقبال'
            else:
                institution_column = 'المؤسسة_الأصلية'
                
            query_str = f"""
                SELECT رقم_التلميذ, النسب, الإسم, المستوى, تاريخ_التحويل,
                       {institution_column}, المديرية_الإقليمية_الأصلية, ملاحظات, id
                FROM سجلات_الوافدين_والمغادرين
                WHERE ملاحظات = '{default_filter}' AND السنة_الدراسية = '{first_year}'
                ORDER BY id DESC
            """

            if self.using_qsql:
                query = QSqlQuery(self.db)
                if not query.exec_(query_str):
                    raise Exception(f"خطأ في تنفيذ الاستعلام: {query.lastError().text()}")

                records = []
                while query.next():
                    record = []
                    for i in range(9):  # 9 أعمدة
                        value = query.value(i)
                        record.append(str(value) if value is not None else '')
                    records.append(record)
            else:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute(query_str)
                records = cursor.fetchall()
                conn.close()
                
                # تحويل None إلى نص فارغ
                records = [[str(value) if value is not None else '' for value in record] for record in records]

            # إرسال البيانات إلى JavaScript مع نوع التصفية
            records_json = json.dumps(records, ensure_ascii=False)
            self.web_view.page().runJavaScript(f"updateTable({records_json}, '{default_filter}');")
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.bridge.showMessage(
                f"حدث خطأ أثناء تحميل البيانات: {str(e)}",
                "error"
            )
            
    def preview_and_print(self):
        """معاينة وطباعة التقرير"""
        try:
            # إنشاء المجلد الرئيسي على سطح المكتب
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
            if not os.path.exists(main_folder):
                os.makedirs(main_folder)

            # إنشاء المجلد الفرعي لتقارير طلبات الشواهد المدرسية
            reports_dir = os.path.join(main_folder, "تقارير طلبات الشواهد المدرسية")
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # عرض رسالة انتظار
            QApplication.setOverrideCursor(Qt.WaitCursor)

            # استخدام دالة الطباعة المتطورة للوافدين والمغادرين
            if ARRIVALS_PDF_AVAILABLE:
                filepath = arrivals_print_function(auto_open=False)
                result = filepath is not None
            else:
                self.bridge.showMessage(
                    "ملف الطباعة المتطور arrivals_departures_report.py غير متوفر",
                    "error"
                )
                return

            # إعادة المؤشر إلى الوضع الطبيعي
            QApplication.restoreOverrideCursor()

            if result and os.path.exists(filepath):
                # إرسال رسالة نجاح وفتح الملف مباشرة
                self.bridge.showMessage(
                    f"تم إنشاء التقرير بنجاح وحفظه في المسار: {filepath}",
                    "success"
                )
                
                # فتح الملف مباشرة
                try:
                    os.startfile(filepath)
                except Exception as e:
                    self.bridge.showMessage(f"حدث خطأ أثناء فتح الملف: {str(e)}", "error")
            else:
                if not filepath:
                    pass  # إلغاء من المستخدم
                elif not os.path.exists(filepath):
                    self.bridge.showMessage(
                        "حدث خطأ أثناء طباعة التقرير - لم يتم إنشاء الملف بشكل صحيح",
                        "warning"
                    )

        except Exception as e:
            self.bridge.showMessage(f"حدث خطأ أثناء طباعة التقرير: {str(e)}", "error")
    
    def delete_certificates(self, selected_codes):
        """حذف السجلات المحددة مع التحقق من رمز التأكيد"""
        try:
            if not selected_codes:
                self.bridge.showMessage(
                    "الرجاء تحديد سجل واحد على الأقل لحذفه",
                    "warning"
                )
                return

            # تأكد من أن selected_codes هو قائمة صحيحة
            if isinstance(selected_codes, str):
                try:
                    selected_codes = json.loads(selected_codes)
                except:
                    selected_codes = [selected_codes]
            elif not isinstance(selected_codes, list):
                selected_codes = list(selected_codes) if selected_codes else []

            print(f"أكواد السجلات المحددة للحذف: {selected_codes}")

            # عرض رسالة تحذير محسنة
            confirm_dialog = QMessageBox(self)
            confirm_dialog.setWindowTitle("🗑️ تأكيد حذف السجلات")
            confirm_dialog.setText("تحذير: حذف السجلات المحددة")
            confirm_dialog.setInformativeText(
                f"ستقوم هذه العملية بحذف {len(selected_codes)} سجل نهائياً:\n\n"
                f"الأكواد المحددة: {', '.join(map(str, selected_codes))}\n\n"
                "⚠️ لا يمكن التراجع عن هذه العملية\n\n"
                "هل تريد المتابعة؟"
            )
            confirm_dialog.setIcon(QMessageBox.Warning)
            confirm_dialog.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            confirm_dialog.setDefaultButton(QMessageBox.No)
            confirm_dialog.button(QMessageBox.Yes).setText("نعم، حذف")
            confirm_dialog.button(QMessageBox.No).setText("إلغاء")
            
            confirm_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                    font-family: 'Calibri';
                    font-size: 13pt;
                }
                QMessageBox QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    color: #c0392b;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 8px;
                    margin: 4px;
                }
            """)

            if confirm_dialog.exec_() != QMessageBox.Yes:
                return

            # طلب رمز التأكيد
            password_dialog = QInputDialog(self)
            password_dialog.setWindowTitle("🔐 التحقق من الهوية")
            password_dialog.setLabelText("الرجاء إدخال رمز التأكيد (12345) للمتابعة:")
            password_dialog.setTextEchoMode(QLineEdit.Password)
            password_dialog.setStyleSheet("""
                QInputDialog {
                    background-color: white;
                    font-family: 'Calibri';
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 15pt;
                    font-weight: bold;
                    color: #2c3e50;
                }
                QLineEdit {
                    padding: 8px;
                    border: 1px solid #bdc3c7;
                    border-radius: 5px;
                    font-family: 'Calibri';
                    font-size: 15pt;
                }
                QPushButton {
                    font-family: 'Calibri';
                    font-size: 15pt;
                    font-weight: bold;
                    min-width: 80px;
                    padding: 5px;
                }
            """)

            ok = password_dialog.exec_()
            password = password_dialog.textValue()

            # التحقق من صحة رمز التأكيد
            if not ok or password != "12345":
                error_dialog = QMessageBox(self)
                error_dialog.setWindowTitle("❌ خطأ في التحقق")
                error_dialog.setText("رمز التأكيد غير صحيح")
                error_dialog.setInformativeText("لم يتم حذف السجلات - الرمز المُدخل غير صحيح")
                error_dialog.setIcon(QMessageBox.Critical)
                error_dialog.setStyleSheet("""
                    QMessageBox { background-color: white; font-family: 'Calibri'; }
                    QLabel { font-family: 'Calibri'; font-size: 13pt; font-weight: bold; color: #c0392b; }
                """)
                error_dialog.exec_()
                return

            # تنفيذ عملية الحذف
            self.process_delete_confirmation(selected_codes)
                
        except Exception as e:
            print(f"خطأ في حذف السجلات: {e}")
            import traceback
            traceback.print_exc()
            self.bridge.showMessage(f"حدث خطأ في حذف السجلات: {str(e)}", "error")
    
    def process_delete_confirmation(self, selected_codes):
        """معالجة حذف السجلات المحددة"""
        try:
            success_count = 0
            error_messages = []

            for code in selected_codes:
                try:
                    if self.using_qsql:
                        query = QSqlQuery(self.db)
                        query.prepare("""
                            DELETE FROM الشهادة_المدرسية
                            WHERE الرمز = ?
                        """)
                        query.addBindValue(code)
                        if not query.exec_():
                            error_messages.append(f"خطأ في حذف السجل رقم {code}: {query.lastError().text()}")
                            continue
                    else:
                        conn = sqlite3.connect(self.db_path)
                        cursor = conn.cursor()
                        cursor.execute("""
                            DELETE FROM الشهادة_المدرسية
                            WHERE الرمز = ?
                        """, (code,))
                        conn.commit()
                        conn.close()

                    success_count += 1
                except Exception as e:
                    error_messages.append(f"خطأ في حذف الرمز {code}: {str(e)}")

            # عرض رسالة النتيجة محسنة
            if success_count > 0:
                success_dialog = QMessageBox(self)
                success_dialog.setWindowTitle("✅ نجح الحذف")
                success_dialog.setText("تم حذف السجلات بنجاح!")
                
                details_text = f"📊 نتائج الحذف:\n\n"
                details_text += f"✅ تم حذف {success_count} سجل بنجاح\n"
                details_text += f"📋 الأكواد المحذوفة: {', '.join(map(str, selected_codes[:10]))}"
                
                if len(selected_codes) > 10:
                    details_text += f" و {len(selected_codes) - 10} آخرين"
                
                if error_messages:
                    details_text += f"\n\n⚠️ أخطاء: {len(error_messages)}\n"
                    details_text += "\n".join(error_messages[:3])
                    if len(error_messages) > 3:
                        details_text += f"\n... و {len(error_messages) - 3} أخطاء أخرى"
                
                success_dialog.setInformativeText(details_text)
                success_dialog.setIcon(QMessageBox.Information)
                success_dialog.setStandardButtons(QMessageBox.Ok)
                
                # إضافة أيقونة البرنامج إلى نافذة الرسالة
                icon_path = "01.ico"
                if os.path.exists(icon_path):
                    success_dialog.setWindowIcon(QIcon(icon_path))

                success_dialog.setStyleSheet("""
                    QMessageBox {
                        background-color: white;
                        font-family: 'Calibri';
                    }
                    QLabel {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        color: #27ae60;
                    }
                    QPushButton {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        font-weight: bold;
                        color: white;
                        background-color: #27ae60;
                        border: none;
                        border-radius: 5px;
                        padding: 8px 15px;
                        min-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #219653;
                    }
                    QPushButton:pressed {
                        background-color: #1e7e34;
                    }
                """)
                
                success_dialog.exec_()

                # تحديث البيانات فقط
                self.load_data()

            elif error_messages:
                error_dialog = QMessageBox(self)
                error_dialog.setWindowTitle("❌ فشل في الحذف")
                error_dialog.setText("لم يتم حذف أي سجل")
                
                error_details = "حدثت الأخطاء التالية:\n\n"
                error_details += "\n".join(error_messages[:5])
                if len(error_messages) > 5:
                    error_details += f"\n... و {len(error_messages) - 5} أخطاء أخرى"

                error_dialog.setInformativeText(error_details)
                error_dialog.setIcon(QMessageBox.Critical)
                error_dialog.setStyleSheet("""
                    QMessageBox {
                        background-color: white;
                        font-family: 'Calibri';
                    }
                    QLabel {
                        font-family: 'Calibri';
                        font-size: 13pt;
                        color: #c0392b;
                        font-weight: bold;
                    }
                """)
                error_dialog.exec_()

        except Exception as e:
            error_dialog = QMessageBox(self)
            error_dialog.setWindowTitle("❌ خطأ في العملية")
            error_dialog.setText("حدث خطأ أثناء حذف السجلات")
            error_dialog.setInformativeText(f"تفاصيل الخطأ:\n{str(e)}")
            error_dialog.setIcon(QMessageBox.Critical)
            error_dialog.setStyleSheet("""
                QMessageBox {
                    background-color: white;
                    font-family: 'Calibri';
                }
                QLabel {
                    font-family: 'Calibri';
                    font-size: 13pt;
                    color: #c0392b;
                    font-weight: bold;
                }
            """)
            error_dialog.exec_()

    def ensure_maximized(self):
        """التأكد من عرض النافذة في كامل الشاشة"""
        self.showMaximized()
        QApplication.processEvents()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق ثيم داكن اختياري
    app.setStyle('Fusion')
    
    window = SchoolCertificateHtmlWindow()
    window.show()
    sys.exit(app.exec_())
