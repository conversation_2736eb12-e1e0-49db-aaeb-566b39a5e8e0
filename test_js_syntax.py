#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def check_js_syntax():
    """فحص بناء JavaScript في الملف"""
    print("فحص بناء JavaScript في sub199_window_html.py")

    try:
        with open('sub199_window_html.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # البحث عن JavaScript
        js_pattern = r'<script[^>]*>(.*?)</script>'
        js_matches = re.findall(js_pattern, content, re.DOTALL)

        if not js_matches:
            print("لم يتم العثور على كود JavaScript")
            return False

        js_code = js_matches[0]
        print(f"تم العثور على {len(js_code)} حرف من كود JavaScript")

        # البحث عن الدوال المطلوبة
        required_functions = ['populateYearFilter', 'updateTable', 'deleteCertificates']
        for func in required_functions:
            if f'function {func}' in js_code:
                print(f"الدالة {func} موجودة")
            else:
                print(f"الدالة {func} مفقودة")

        # البحث عن رموز مشوهة
        if '�' in js_code:
            lines = js_code.split('\n')
            for i, line in enumerate(lines):
                if '�' in line:
                    print(f"رمز مشوه في السطر {i+1}: {line.strip()[:50]}")
        else:
            print("لا توجد رموز مشوهة")

        return True

    except Exception as e:
        print(f"خطأ في فحص الملف: {e}")
        return False

if __name__ == "__main__":
    check_js_syntax()
