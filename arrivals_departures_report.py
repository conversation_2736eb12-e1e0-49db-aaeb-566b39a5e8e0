"""
تقارير PDF لسجلات الوافدين والمغادرين مع دعم كامل للغة العربية
"""

import os
import sys
import sqlite3
from datetime import datetime
import subprocess
from database_config import get_database_path, get_database_connection

try:
    from fpdf import FPDF
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    print("المكتبات المطلوبة غير متوفرة. جاري تثبيتها...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "fpdf2", "arabic-reshaper", "python-bidi"])
        from fpdf import FPDF
        import arabic_reshaper
        from bidi.algorithm import get_display
        print("تم تثبيت المكتبات بنجاح!")
    except Exception as e:
        print(f"فشل تثبيت المكتبات: {e}")
        sys.exit(1)

class ArabicArrivalsDepPDF(FPDF):
    """فئة مخصصة لإنشاء ملفات PDF لسجلات الوافدين والمغادرين مع دعم اللغة العربية"""

    def __init__(self, orientation='L', unit='mm', format='A4'):  # L = Landscape للصفحة الأفقية
        super().__init__(orientation, unit, format)
        self.set_margins(2, 2, 2)  # هوامش 0.2 سم (2مم) من جميع النواحي
        self.set_auto_page_break(True, 2)  # هامش سفلي 0.2 سم

        self.current_page = 0
        self._setup_arabic_fonts()
        self.institution_data = None
        self.main_title = "سجل الوافدين والمغادرين"
        self.subtitle = None
        self.dark_blue = (0, 51, 153)  # تعريف اللون الأزرق الغامق - RGB (0, 51, 153)

    def _setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
        if not os.path.exists(fonts_dir):
            os.makedirs(fonts_dir)

        arabic_fonts = [
            ["fonts/amiri-regular.ttf", "Amiri"],
            ["fonts/arial.ttf", "Arial"],
            ["fonts/calibri.ttf", "Calibri"],
            ["fonts/tahoma.ttf", "Tahoma"]
        ]

        self.arabic_font_added = False

        for font_path, font_name in arabic_fonts:
            full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), font_path)
            if os.path.exists(full_path):
                try:
                    self.add_font(font_name, '', full_path, uni=True)
                    print(f"تم تحميل الخط العربي: {font_name}")
                    self.arabic_font_name = font_name
                    self.arabic_font_added = True
                    break
                except Exception as e:
                    print(f"خطأ في تحميل الخط {font_name}: {e}")

        if not self.arabic_font_added:
            amiri_path = os.path.join(fonts_dir, "amiri-regular.ttf")
            try:
                print("لم يتم العثور على خطوط عربية. جاري تنزيل خط Amiri...")
                import urllib.request
                amiri_url = "https://github.com/alif-type/amiri/raw/master/AmiriQuran.ttf"
                urllib.request.urlretrieve(amiri_url, amiri_path)
                self.add_font("Amiri", '', amiri_path, uni=True)
                print("تم تنزيل وتحميل خط Amiri بنجاح!")
                self.arabic_font_name = "Amiri"
                self.arabic_font_added = True
            except Exception as e:
                print(f"فشل تنزيل أو تحميل خط Amiri: {e}")
                self.arabic_font_name = "Helvetica"

        calibri_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "calibri.ttf")
        calibri_bold_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts", "calibrib.ttf")

        if os.path.exists(calibri_path):
            try:
                self.add_font('Calibri', '', calibri_path, uni=True)
                self.arabic_font_name = 'Calibri'
                self.arabic_font_added = True
                print("تم تحميل خط Calibri بنجاح")

                if os.path.exists(calibri_bold_path):
                    self.add_font('Calibri-Bold', '', calibri_bold_path, uni=True)  # نفس طريقة arabic_pdf_report
                    print("تم تحميل خط Calibri Bold بنجاح")
            except Exception as e:
                print(f"خطأ في تحميل خط Calibri: {e}")

    def load_institution_data(self):
        """تحميل بيانات المؤسسة من قاعدة البيانات"""
        try:
            db_path = get_database_path()
            if os.path.exists(db_path):
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='بيانات_المؤسسة'")
                if cursor.fetchone():
                    cursor.execute("SELECT ImagePath1, المؤسسة, السنة_الدراسية FROM بيانات_المؤسسة LIMIT 1")
                    row = cursor.fetchone()
                    if row:
                        self.institution_data = {
                            "logo_path": row[0],
                            "name": row[1],
                            "academic_year": row[2] if len(row) > 2 and row[2] else "السنة الدراسية غير محددة"
                        }
                        print(f"تم تحميل بيانات المؤسسة: {self.institution_data['name']}, السنة الدراسية: {self.institution_data['academic_year']}")
                else:
                    print("جدول بيانات المؤسسة غير موجود")

                conn.close()
        except Exception as e:
            print(f"خطأ في تحميل بيانات المؤسسة: {e}")

    def header(self):
        """ترويسة لكل صفحة تتضمن شعار المؤسسة واسمها والعنوان"""
        self.current_page += 1

        if self.institution_data is None:
            self.load_institution_data()

        center_x = self.w / 2
        current_y = 5  # تقليل البداية من 10 إلى 5

        # إضافة الشعار أولاً (في الأعلى)
        if self.institution_data and self.institution_data["logo_path"]:
            logo_path = self.institution_data["logo_path"]
            if os.path.exists(logo_path):
                logo_width = 50  # تقليل عرض الشعار من 60 إلى 50
                logo_height = 20  # تقليل ارتفاع الشعار من 25 إلى 20
                logo_x = center_x - (logo_width / 2)
                self.image(logo_path, x=logo_x, y=current_y, w=logo_width, h=logo_height)
                current_y += logo_height + 4  # تقليل المساحة بعد الشعار من 8 إلى 4

        # اسم المؤسسة
        if self.institution_data and self.institution_data["name"]:
            institution_name = self.institution_data["name"]
            institution_display = get_display(arabic_reshaper.reshape(institution_name))

            self.set_font(self.arabic_font_name, size=12)  # حجم الخط مناسب
            name_width = self.get_string_width(institution_display)
            name_x = center_x - (name_width / 2)
            self.set_xy(name_x, current_y)
            self.set_text_color(*self.dark_blue)
            self.cell(name_width, 5, institution_display, align='C')  # تقليل ارتفاع الخلية من 6 إلى 5
            current_y += 6  # تقليل المساحة بعد اسم المؤسسة من 10 إلى 6

        # العنوان الرئيسي
        if self.main_title:
            main_title_display = get_display(arabic_reshaper.reshape(self.main_title))

            self.set_font(self.arabic_font_name, size=14)  # تكبير حجم خط العنوان من 11 إلى 14
            title_width = self.get_string_width(main_title_display)
            title_x = center_x - (title_width / 2)
            self.set_xy(title_x, current_y)
            self.set_text_color(0, 0, 0)  # أسود
            self.cell(title_width, 6, main_title_display, align='C')  # زيادة ارتفاع الخلية من 5 إلى 6
            current_y += 7  # زيادة المساحة بعد العنوان من 6 إلى 7

        # حذف العنوان الفرعي والخط الفاصل لتوفير مساحة أكبر
        
        # تقليل المساحة قبل بداية الجدول
        self.set_y(current_y + 2)  # تقليل من 4 إلى 2
        
        # رقم الصفحة (في الزاوية اليمنى العلوية)
        page_text = f"صفحة {self.current_page}"
        page_display = get_display(arabic_reshaper.reshape(page_text))
        
        self.set_font(self.arabic_font_name, size=9)
        self.set_text_color(100, 100, 100)
        self.set_xy(self.w - 40, 8)
        self.cell(30, 5, page_display, align='R')

    def format_text(self, text):
        """تنسيق النص العربي للعرض الصحيح"""
        if not text or str(text).strip() == '' or str(text) == 'None':
            return '-'
        
        text = str(text).strip()
        reshaped = arabic_reshaper.reshape(text)
        return get_display(reshaped)

    def add_table_headers(self, headers, col_widths):
        """إضافة عناوين الجدول - بخط Calibri 14 أبيض غامق"""
        # التأكد من أن الموضع أسفل الترويسة المختصرة
        if self.get_y() < 25:  # تقليل المسافة المطلوبة من 45 إلى 25
            self.set_y(25)  # انتقال إلى موضع أقرب من الترويسة
            
        # تعيين لون أزرق داكن للخلفية (نفس لون arabic_pdf_report)
        self.set_fill_color(0, 51, 153)  # لون أزرق داكن للرأس
        self.set_text_color(255, 255, 255)  # نص أبيض
        
        # استخدام خط Calibri 14 (نفس arabic_pdf_report)
        font_to_use = 'Calibri-Bold' if hasattr(self, 'fonts') and 'Calibri-Bold' in self.fonts else self.arabic_font_name
        self.set_font(font_to_use, '', 14)
        self.set_draw_color(0, 0, 0)  # خطوط أسود غامق
        self.set_line_width(0.3)  # نفس سمك الخط في arabic_pdf_report

        for i, (header, width) in enumerate(zip(headers, col_widths)):
            header_display = self.format_text(header)
            self.cell(width, 10, header_display, 1, 0, 'C', True)
        
        self.ln()
        self.set_text_color(0, 0, 0)  # إعادة تعيين لون النص للأسود

    def add_table_row(self, row_data, col_widths, fill=False):
        """إضافة صف بيانات للجدول - بخط Calibri 12 أسود غامق"""
        # استخدام خط Calibri 12 (نفس arabic_pdf_report)
        font_to_use = 'Calibri-Bold' if hasattr(self, 'fonts') and 'Calibri-Bold' in self.fonts else self.arabic_font_name
        self.set_font(font_to_use, '', 12)
        
        if fill:
            self.set_fill_color(240, 240, 240)  # لون رمادي فاتح (نفس arabic_pdf_report)
        else:
            self.set_fill_color(255, 255, 255)  # لون أبيض
        
        self.set_text_color(0, 0, 0)  # نص أسود غامق
        self.set_draw_color(0, 0, 0)  # خطوط أسود غامق
        self.set_line_width(0.3)  # نفس سمك الخط في arabic_pdf_report

        for i, (data, width) in enumerate(zip(row_data, col_widths)):
            data_display = self.format_text(data)
            self.cell(width, 8, data_display, 1, 0, 'C', fill)
        
        self.ln()

    def add_signature_section(self, total_count=0, delivered_count=0):
        """إضافة قسم التوقيعات والإحصائيات"""
        # إضافة مساحة فارغة قبل قسم التوقيعات
        self.ln(10)
        
        # إحصائيات
        stats_y = self.get_y()
        
        # الإحصائيات في الجانب الأيمن
        self.set_font(self.arabic_font_name, size=12)
        self.set_text_color(0, 0, 0)
        
        # إجمالي السجلات
        total_text = f"إجمالي السجلات: {total_count}"
        total_display = get_display(arabic_reshaper.reshape(total_text))
        self.set_xy(self.w - 80, stats_y)
        self.cell(70, 8, total_display, 0, 0, 'R')
        
        # معلومات التاريخ في الجانب الأيسر
        current_date = datetime.now().strftime("%Y-%m-%d")
        date_text = f"تاريخ التقرير: {current_date}"
        date_display = get_display(arabic_reshaper.reshape(date_text))
        self.set_xy(20, stats_y)
        self.cell(70, 8, date_display, 0, 0, 'L')
        
        # خط فاصل
        self.ln(15)
        line_y = self.get_y()
        self.set_draw_color(100, 100, 100)
        self.set_line_width(0.5)
        margin = 50
        self.line(margin, line_y, self.w - margin, line_y)
        
        # قسم التوقيعات
        self.ln(10)
        signature_y = self.get_y()
        
        self.set_font(self.arabic_font_name, size=11)
        
        # توقيع المدير (يمين)
        director_text = "توقيع المدير"
        director_display = get_display(arabic_reshaper.reshape(director_text))
        self.set_xy(self.w - 80, signature_y)
        self.cell(60, 8, director_display, 0, 0, 'C')
        
        # خط للتوقيع (يمين)
        self.line(self.w - 80, signature_y + 15, self.w - 20, signature_y + 15)
        
        # توقيع الحارس العام (يسار)
        guard_text = "توقيع الحارس العام"
        guard_display = get_display(arabic_reshaper.reshape(guard_text))
        self.set_xy(20, signature_y)
        self.cell(60, 8, guard_display, 0, 0, 'C')
        
        # خط للتوقيع (يسار)
        self.line(20, signature_y + 15, 80, signature_y + 15)

def download_arabic_fonts():
    """تحميل الخطوط العربية إذا لم تكن موجودة"""
    fonts_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "fonts")
    if not os.path.exists(fonts_dir):
        os.makedirs(fonts_dir)
    
    print("تم التأكد من وجود مجلد الخطوط")

def create_arrivals_departures_report(records=None, output_dir=None, auto_open=False):
    """إنشاء تقرير الوافدين والمغادرين باستخدام FPDF"""
    download_arabic_fonts()

    # استخدام المجلد المحدد إذا تم تمريره، وإلا استخدام المجلد الافتراضي
    if output_dir and os.path.exists(output_dir):
        reports_dir = output_dir
    else:
        reports_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "تقارير_الوافدين_والمغادرين")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

    current_datetime = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(reports_dir, f"تقرير_الوافدين_والمغادرين_{current_datetime}.pdf")
    
    if records is None:
        try:
            db_path = get_database_path()
            if os.path.exists(db_path):
                print("جاري استرجاع البيانات من قاعدة البيانات...")
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # استعلام للحصول على الأعمدة المطلوبة مع التصفية
                cursor.execute("""
                    SELECT رقم_التلميذ, النسب, الإسم, المستوى, 
                           تاريخ_التحويل, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية
                    FROM سجلات_الوافدين_والمغادرين
                    WHERE ملاحظات = 'لائحة التحويلات (الوافدون)'
                    ORDER BY id ASC
                """)
                
                raw_records = cursor.fetchall()
                conn.close()
                
                # دمج الإسم والنسب وإضافة عمود الترتيب
                records = []
                for i, record in enumerate(raw_records, 1):
                    # دمج النسب والإسم في عمود واحد
                    name_combined = f"{record[2]} {record[1]}".strip()  # الإسم + النسب
                    
                    # إنشاء السجل الجديد: [الترتيب، رقم_التلميذ، الاسم_والنسب، المستوى، تاريخ_التحويل، المؤسسة_الأصلية، المديرية_الإقليمية_الأصلية]
                    new_record = (
                        i,                    # الترتيب
                        record[0],            # رقم_التلميذ
                        name_combined,        # الاسم والنسب (مدمج)
                        record[3],            # المستوى
                        record[4],            # تاريخ_التحويل
                        record[5],            # المؤسسة_الأصلية
                        record[6]             # المديرية_الإقليمية_الأصلية
                    )
                    records.append(new_record)
                
                if not records:
                    print("لا توجد بيانات وافدين في قاعدة البيانات. سيتم استخدام بيانات تجريبية.")
                    records = generate_test_arrivals_data()
            else:
                print("قاعدة البيانات غير موجودة. سيتم استخدام بيانات تجريبية.")
                records = generate_test_arrivals_data()
        except Exception as e:
            print(f"خطأ في قراءة قاعدة البيانات: {e}")
            records = generate_test_arrivals_data()

    pdf = ArabicArrivalsDepPDF('L', 'mm', 'A4')
    pdf.set_auto_page_break(False, margin=12)

    # تحميل بيانات المؤسسة لضمان تحميل السنة الدراسية
    if pdf.institution_data is None:
        pdf.load_institution_data()

    # دمج السنة الدراسية مع العنوان الرئيسي
    academic_year = ""
    if pdf.institution_data and "academic_year" in pdf.institution_data:
        academic_year = pdf.institution_data["academic_year"]
    else:
        academic_year = "السنة الدراسية غير محددة"

    # إنشاء العنوان المدمج
    pdf.main_title = f"سجل الوافدين للسنة الدراسية {academic_year}"
    pdf.subtitle = None  # إزالة العنوان الفرعي لتوفير مساحة

    pdf.add_page()

    # عناوين الجدول (بالترتيب المنطقي)
    headers_logical = ["الترتيب", "رقم التلميذ", "الاسم والنسب", "المستوى", "تاريخ التحويل", "المؤسسة الأصلية", "المديرية الإقليمية الأصلية"]
    col_widths_logical = [15, 27, 35, 60, 28, 70, 45]  # رقم التلميذ: 27 (+5) للوضوح أكثر

    # عكس الترتيب للعرض من اليمين إلى اليسار
    display_headers = headers_logical[::-1]
    display_col_widths = col_widths_logical[::-1]

    table_total_width = sum(col_widths_logical)
    printable_page_width = pdf.w - pdf.l_margin - pdf.r_margin
    centering_offset = (printable_page_width - table_total_width) / 2
    if centering_offset < 0:
        centering_offset = 0

    # التأكد من أن الموضع صحيح قبل بدء الجدول
    # تطبيق نفس منطق الصفحات التالية على الصفحة الأولى
    pdf.set_y(45)  # نفس موضع الصفحات التالية
    pdf.set_x(pdf.l_margin + centering_offset)
    pdf.add_table_headers(display_headers, display_col_widths)

    current_page_actual_height = pdf.h
    header_actual_consumption = 75  # زيادة المساحة المحجوزة للترويسة
    footer_margin_space = 20  # زيادة المساحة المحجوزة للتوقيعات
    data_row_height = 8

    # تحديد عدد السجلات في كل صفحة إلى 15 سجل ثابت
    rows_per_page = 15  # زيادة من 10 إلى 15 سجل لكل صفحة

    print(f"إنشاء تقرير بـ {len(records)} سجل - حد أقصى {rows_per_page} صفوف في كل صفحة")

    row_count = 0
    page_number = 1

    for i, record_logical in enumerate(records):
        if row_count >= rows_per_page and rows_per_page > 0:
            page_number += 1
            pdf.add_page()
            # التأكد من الموضع الصحيح في الصفحة الجديدة (مع التخطيط المختصر)
            pdf.set_y(45)  # نفس موضع الصفحة الأولى
            pdf.set_x(pdf.l_margin + centering_offset)
            pdf.add_table_headers(display_headers, display_col_widths)
            row_count = 0
            print(f"إنشاء صفحة جديدة رقم {page_number}")

        pdf.set_x(pdf.l_margin + centering_offset)

        # عكس ترتيب البيانات للعرض من اليمين إلى اليسار
        display_record = record_logical[::-1]
        pdf.add_table_row(display_record, display_col_widths, fill=(i % 2 == 0))
        row_count += 1

    pdf.add_signature_section(total_count=len(records))

    print(f"تم إنشاء التقرير بنجاح: {page_number} صفحة تحتوي على {len(records)} سجل وافد.")

    pdf.output(output_path)
    print(f"تم إنشاء التقرير بنجاح: {output_path}")

    # فتح الملف تلقائيًا فقط إذا كانت المعلمة auto_open تساوي True
    if auto_open:
        try:
            os.startfile(output_path)
        except Exception as e:
            print(f"خطأ في فتح الملف: {e}")

    return output_path

def generate_test_arrivals_data():
    """توليد بيانات تجريبية للوافدين"""
    test_data = []
    for i in range(1, 51):  # 50 سجل تجريبي
        # دمج الاسم والنسب في عمود واحد
        name_combined = f"اسم الطالب الوافد {i} عائلة رقم {i}"
        
        record_logical = [
            i,                              # الترتيب
            f"T{i:04d}",                    # رقم التلميذ
            name_combined,                  # الاسم والنسب (مدمج)
            f"المستوى {(i%6)+1}",          # المستوى
            f"2024-0{(i%9)+1}-{(i%28)+1:02d}",  # تاريخ التحويل
            f"المؤسسة الأصلية رقم {i}",     # المؤسسة الأصلية
            f"المديرية الإقليمية رقم {(i%5)+1}"  # المديرية الإقليمية الأصلية
        ]
        test_data.append(record_logical)
    return test_data

def print_arrivals_departures_requests(parent=None):
    """طباعة سجلات الوافدين والمغادرين - وظيفة رئيسية للاستدعاء من النوافذ الأخرى"""
    try:
        # إنشاء المجلد الرئيسي على سطح المكتب
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        main_folder = os.path.join(desktop_path, "تقارير برنامج المعين في الحراسة العامة")
        if not os.path.exists(main_folder):
            os.makedirs(main_folder)

        # إنشاء المجلد الفرعي لتقارير الوافدين والمغادرين
        reports_dir = os.path.join(main_folder, "تقارير الوافدين والمغادرين")
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # إنشاء مجلد احتياطي في حالة فشل الوصول إلى سطح المكتب
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "تقارير_الوافدين_والمغادرين")
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # التحقق من إمكانية الكتابة في المجلد على سطح المكتب
        try:
            test_file = os.path.join(reports_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            print(f"تعذر الكتابة في مجلد سطح المكتب: {e}")
            reports_dir = backup_dir

        # جلب البيانات من قاعدة البيانات
        try:
            db_path = get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT رقم_التلميذ, النسب, الإسم, المستوى, 
                       تاريخ_التحويل, المؤسسة_الأصلية, المديرية_الإقليمية_الأصلية
                FROM سجلات_الوافدين_والمغادرين
                WHERE ملاحظات = 'لائحة التحويلات (الوافدون)'
                ORDER BY id ASC
            """)

            raw_records = cursor.fetchall()
            conn.close()

            # دمج الإسم والنسب وإضافة عمود الترتيب
            records = []
            for i, record in enumerate(raw_records, 1):
                # دمج النسب والإسم في عمود واحد
                name_combined = f"{record[2]} {record[1]}".strip()  # الإسم + النسب
                
                # إنشاء السجل الجديد
                new_record = (
                    i,                    # الترتيب
                    record[0],            # رقم_التلميذ
                    name_combined,        # الاسم والنسب (مدمج)
                    record[3],            # المستوى
                    record[4],            # تاريخ_التحويل
                    record[5],            # المؤسسة_الأصلية
                    record[6]             # المديرية_الإقليمية_الأصلية
                )
                records.append(new_record)

            if not records:
                print("لا توجد سجلات وافدين للطباعة")
                return False, "", ""
                
        except Exception as e:
            print(f"خطأ في قراءة قاعدة البيانات: {e}")
            return False, "", ""

        # إنشاء التقرير
        output_path = create_arrivals_departures_report(records, reports_dir, auto_open=False)
        if output_path and os.path.exists(output_path):
            print(f"تمت طباعة {len(records)} سجل وافد بنجاح")
            print(f"الملف تم حفظه في: {output_path}")
            return True, output_path, reports_dir
        else:
            print("فشل في إنشاء التقرير")
            return False, "", ""
            
    except Exception as e:
        print(f"خطأ أثناء طباعة تقرير الوافدين والمغادرين: {e}")
        if parent:
            try:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "خطأ", f"حدث خطأ أثناء طباعة تقرير الوافدين والمغادرين:\n{str(e)}")
            except ImportError:
                pass
        return False, "", ""

if __name__ == "__main__":
    print("===== إنشاء تقرير الوافدين والمغادرين باستخدام FPDF =====")
    create_arrivals_departures_report(auto_open=True)